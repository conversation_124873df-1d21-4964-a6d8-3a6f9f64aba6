"""
* Copyright (c) 2023 ZOOMi Technologies Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* utils functions used here are used to assist agent service

* @description utils functions used here are used to assist agent service
* <AUTHOR>

"""

import ast
import json
from logging import Logger
import os
import pickle
import re
import traceback
import types
import base64
import time
import xxhash
from services.llm_client import LLMClient
from utils.constant import ResponseKeywords, IMAGE_EXTENSIONS, FILE_EXTENSIONS
import openai
from openai import RateLimitError, InternalServerError, OpenAIError, AuthenticationError, APIError


# Global cache
llm_cache = {}

# Load the cache from pickle
if os.path.exists("data/llm_cache.pkl"):
    with open("data/llm_cache.pkl", "rb") as f:
        llm_cache = pickle.load(f)


def get_utility_functions(layenext_client):
    llm_utility_functions = {
        "retrieve_documents": layenext_client.retrieve_documents,
        "run_mongodb_aggregation": layenext_client.run_mongodb_aggregation,
        "extract_document_keyInfo_llm": extract_document_keyInfo_llm,
        "run_sql_query": layenext_client.run_sql_query,
        "retrieve_text_chunks": layenext_client.retrieve_text_chunks,
    }
    return llm_utility_functions


# LLM Cache
def read_llm_cache(llm_prompt, conversation_id, output_type=None):
    # Replace conversation id with dummy value before caching
    llm_prompt_str = json.dumps(llm_prompt)
    llm_prompt_str = llm_prompt_str.replace(conversation_id, "<<dummy_conversation_id>>")
    # Create hash with 128 bits of the prompt
    llm_prompt_hash = xxhash.xxh128(llm_prompt_str).hexdigest()
    if llm_prompt_hash in llm_cache:
        cached_response = llm_cache[llm_prompt_hash]
        if output_type is None:
            return cached_response
        else:
            return output_type(cached_response)
    return None


def write_llm_cache(llm_prompt, llm_response, conversation_id):

    # Replace conversation id with dummy value before caching
    llm_prompt_str = json.dumps(llm_prompt)
    llm_prompt_str = llm_prompt_str.replace(conversation_id, "<<dummy_conversation_id>>")
    # Create hash with 128 bits of the prompt
    llm_prompt_hash = xxhash.xxh128(llm_prompt_str).hexdigest()
    if llm_prompt_hash in llm_cache:
        return  # Already cached
    llm_cache[llm_prompt_hash] = llm_response
    # Save this to pickle
    with open("data/llm_cache.pkl", "wb") as f:
        pickle.dump(llm_cache, f)


#


def extract_document_keyInfo_llm(
    open_ai_client,
    open_ai_model_name,
    document_chunk_list,
    filtering_keywords,
    answering_keys,
    user_question,
    instruction_file="llm_prompt_info_extraction.txt",
):
    # the key information extraction invoke llm to isolate the key info from docs, relevant to user question
    # the final output will be a list of dictionaries, each dict corresponding to one doc

    with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
        instructions = file.read()

    instructions = instructions.replace("<<filtering_keys>>", str(filtering_keywords))
    instructions = instructions.replace("<<answering_keys>>", str(answering_keys))
    instructions = instructions.replace("<<user_question>>", str(user_question))
    llm_prompt = []
    llm_prompt.append({"role": "system", "content": instructions})
    # llm_prompt.append({"role": "user", "content": f"Extract key data for this list of search keys {str(answering_keys)}."})
    llm_prompt.append({"role": "user", "content": "list of chunks :" + str(document_chunk_list)})
    response = open_ai_client.chat.completions.create(
        model=open_ai_model_name, seed=1234, messages=llm_prompt, max_tokens=4096, temperature=0.7
    )
    llm_output = response.choices[0].message.content

    if check_unrelevent(llm_output):
        return -1
    else:
        list_of_dicts = find_outermost_dictionaries(llm_output)
        return list_of_dicts


def find_outermost_dictionaries(text):
    dictionaries = []
    stack = []
    start_index = None

    for i, char in enumerate(text):
        if char == "{":
            if not stack:
                start_index = i
            stack.append("{")
        elif char == "}":
            if stack:
                stack.pop()
                if not stack and start_index is not None:
                    dictionaries.append(text[start_index : i + 1])
                    start_index = None
                    # dict = ast.literal_eval(dict_str)
    python_dicts = []
    for dict_str in dictionaries:
        try:
            dict = ast.literal_eval(dict_str)
            python_dicts.append(dict)
        except:
            print("invalid dictionary")

    return python_dicts


def extract_file_path_and_markdown_content(input_str):
    """
    description:Function to extract the file path(s) and markdown content or other text
    parameter  :
                input_str   : response from python code execution
    return:
                file_path : path of the saved file or a comma separated string
                markdown_content : For table related responses, the markdown table
                                For document related responses this can be general text
    """
    # Return if there is no  FILE: part
    if "FILE:" not in input_str:
        return None, input_str

    # check if there are multiple FILE: keywords
    matches = re.findall(r"FILE:\s*\S+", input_str)
    is_file_path_at_beginning = False
    markdown_content = None
    if len(matches) > 1:
        # Handle the string with multiple FILE: keywords
        file_name_regex = re.compile(r"FILE:\s*([^\n,]+)")
        # Find all matches of the regex pattern
        file_names = file_name_regex.findall(input_str)
        file_path = ", ".join(file_names)
        # Remove the matched 'FILE: prts from the original text to get the rest of the text
        markdown_content = re.sub(r"FILE:\s*[^\n,]+", "", input_str)
    else:
        # Handle the string with Single FILE: keyword
        elements = input_str.split("FILE:")
        if elements[0]:
            # mark down content is before the FILE
            file_path = elements[1].strip()
            markdown_content = elements[0].strip()
            markdown_in_file_path = re.sub(r"FILE:\s*[^\n]+", "", "FILE: " + file_path)
            # Checking if the file path contains any content other than file paths.
            if len(markdown_in_file_path.strip()) > 0:
                is_file_path_at_beginning = True
                input_str = "FILE: " + file_path

        if is_file_path_at_beginning or elements[0] == "":
            # FILE is at the beginning
            file_path_match = re.search(
                r"FILE:\s([^\n|]+(?:\n[^\n|]+)*)", input_str
            )  # Ensures the path does not contain newline or '|' characters within a single line.
            file_path = file_path_match.group(1).strip() if file_path_match else None
            # find the start of the markdown content or a newline
            index_pipe = input_str.find("|", file_path_match.end(1))
            index_newline = input_str.find("\n", file_path_match.end(1))
            # If both characters are not found, set the index to -1
            if index_pipe == -1 and index_newline == -1:
                markdown_start_index = -1
            # If one of the characters is not found, use the index of the other character
            elif index_pipe == -1:
                markdown_start_index = index_newline
            elif index_newline == -1:
                markdown_start_index = index_pipe
            # If both characters are found, use the smaller index
            else:
                markdown_start_index = min(index_pipe, index_newline)
            # markdown_start_index = input_str.find("|", file_path_match.end(1)) if file_path_match else -1
            markdown_content = input_str[markdown_start_index:].strip() if markdown_start_index != -1 else None

    # Check if file path has multiple files separated by space instead of comma - then replace it with comma (", ")
    if file_path is not None and "," not in file_path and " " in file_path:
        file_path = ", ".join(file_path.split(" "))
    return file_path, markdown_content


"""
description:Function to count tokens in a text               
parameter  :
            input_text   : text to count tokens
            encoding     : encode method
return: 
                            number of tokens in the text             
"""


def count_tokens(input_text, encoding):
    return len(encoding.encode(input_text))


"""
description:Function to clean the unwanted local variables              
parameter  :
            local_vars   : local variables at the instance
return: 
                            local variables at the instance            
"""


def clean_vars(local_vars):
    ret = local_vars.pop("__builtins__", "Key not found")
    ret = local_vars.pop("action", "Key not found")
    ret = local_vars.pop("self", "Key not found")
    keys_to_remove = []
    for k, v in local_vars.items():
        if isinstance(v, types.ModuleType) or isinstance(v, types.FunctionType):
            keys_to_remove.append(k)
    for key in list(local_vars.keys()):
        if key in keys_to_remove:
            del local_vars[key]
    return local_vars


def check_unrelevent(response):
    if response:
        return "UN_RELEVANT" in response
    else:
        return False


"""
description:Function to check a LLM response is the final one of the chain of thought             
parameter  :
            response   : LLM response which is to be checked
return: 
                            True or False : True if it is the final response of the chain of thought            
"""


def check_finish(response):
    if response:
        return response.startswith(ResponseKeywords.FINISH.value)
    else:
        return False


"""
description:Function to check a LLM response is the media          
parameter  :
            response   : LLM response which is to be checked
return: 
                            True or False : True if it is media          
"""


def check_file(response):
    # return response.startswith(ResponseKeywords.FILE.value)
    if ResponseKeywords.FILE.value in response:
        return True
    else:
        return False


"""
description:Function to check a LLM response is a question asked back from the user             
parameter  :
            response   : LLM response which is to be checked
return: 
                        True or False : True if it is a question asked back from the user         
"""


def check_question(response):
    if ResponseKeywords.QUESTION.value in response:
        return True
    else:
        return False


"""
description: Function to check whether the LLM response is the list of insight categories
returns: True or False - True if this is expected response
"""


def check_insight_categories_list(response):
    if response:
        return ResponseKeywords.INSIGHT_CATEGORIES.value in response
    else:
        return False


"""
description: Function to check whether the LLM response is the list of data sections
returns: True or False - True if this is a data section list response
"""


def check_data_section_list(response):
    if response:
        return ResponseKeywords.SECTIONS.value in response
    else:
        return False


def check_data_section_feedback(response):
    if response:
        return ResponseKeywords.SECTIONS_FEEDBACK.value in response
    else:
        return False


def get_data_sections_as_string(structured_response: dict):
    # Returns the data sections in the format that is referenced in other places of the code as SECTIONS: <DATA_SOURCE_NAME>.<SECTION_NAME>
    if structured_response:
        if "selected_tables" not in structured_response:
            return None
        # Prepare string based on table_selection_output_schema - selected_tables is a list of dictionaries
        sections_list_str = ",".join(
            [f"{table['data_source_name']}.{table['table_name']}" for table in structured_response["selected_tables"]]
        )
        return "SECTIONS: " + sections_list_str
    else:
        return None


"""
description: Function to check whether the LLM response is a list of insight categories
returns: True or False - True if this is a insight category list response
"""


def check_insight_category_list(response):
    if response:
        return ResponseKeywords.INSIGHT_CATEGORIES.value in response
    else:
        return False


"""
description:Function to check a LLM response, if it says LLM is unable to answer the question with the current chain of thought           
parameter  :
            response   : LLM response which is to be checked
return: 
                            True or False : True if it is unable to answer the user question           
"""


def check_unable(response):
    if ResponseKeywords.UNABLE.value in response:
        return True
    else:
        return False


def get_python_actions(text: str, chat_log: Logger = None, llm_extraction_client: LLMClient = None):
    """
    Description: Extracts Python code blocks from LLM output
    Parameters:
        text (str): The text to extract Python code from
        llm_extraction_client (LLMClient): If provided, uses LLM to extract Python code if failed with pattern matching
    Returns:
        str, str: The extracted Python code and data retrieval logic if found, else None
    """
    if text is None:
        return None, None
    # Define the regex pattern to match text encapsulated between ```python and ```
    try:
        pattern_code_with_delimiter = r"```\s*python(.*?)\s*```"
        pattern_code_with_no_delimiter = r"```python(.*?)```"
        pattern_logic_with_delimiter = r"```\s*(?!python)(.*?)\s*```"
        pattern_logic_with_no_delimiter = r"(?<!```python)(?:^|\n)(?!```)(.*?)(?=```|$)"
        matches_code_with_delimiter = re.findall(pattern_code_with_delimiter, text, re.DOTALL)
        matches_code_with_no_delimiter = re.findall(pattern_code_with_no_delimiter, text, re.DOTALL)
        matches_logic_with_delimiter = re.findall(pattern_logic_with_delimiter, text, re.DOTALL)
        matches_logic_with_no_delimiter = re.findall(pattern_logic_with_no_delimiter, text, re.DOTALL)
        matches_code = matches_code_with_delimiter or matches_code_with_no_delimiter
        matches_logic = matches_logic_with_delimiter or matches_logic_with_no_delimiter
        if not matches_code and llm_extraction_client:
            # Use LLM to extract Python code and logic
            code, logic = llm_extract_python_code_and_logic(text, llm_extraction_client, chat_log)
            return code, logic
        elif not matches_code:
            return None, None  # Failure
        else:
            return "\n\n".join(matches_code) if matches_code else None, (
                "\n\n" + matches_logic[0] if matches_logic else None
            )
    except Exception as e:
        print(f"Error in get_python_actions: {traceback.format_exc()}")
        return None, None


def extract_sql_select_queries(code: str):
    """
    Extract SELECT SQL queries from Python code.

    Parameters:
        code (str): The Python code as a string.

    Returns:
        list: A list of extracted SQL SELECT queries.
    """
    # Regular expression to find multiline strings containing SELECT
    sql_query_pattern = r'(f?""".*?SELECT.*?""")'
    try:
        matches = re.findall(sql_query_pattern, code, re.DOTALL | re.IGNORECASE)
        sql_queries = [match.lstrip("f").strip('"""') for match in matches]
        return sql_queries
    except Exception as e:
        print(f"Error in extract_sql_select_queries: {traceback.format_exc()}")
        return []


def llm_extract_python_code_and_logic(text: str, llm_client: LLMClient, chat_log: Logger):
    """
    Description: Extracts Python code block and data retrieval logic from LLM output
    Parameters:
        text (str): The text to extract Python code from
    Returns:
        str, str: The extracted Python code and data retrieval logic if found, else None
    """
    extraction_model = os.getenv("MODEL_JSON_OUTPUT")
    try:
        llm_prompts = [
            {
                "role": "system",
                "content": "You are a helpful assistant that can extract required content precisely from given text and format it as JSON.",
            },
            {"role": "user", "content": "Extract Python code and data retrieval logic from the following text:"},
            {"role": "user", "content": text},
        ]
        response = llm_client.get_llm_response(
            None,
            None,
            chat_log,
            llm_prompts,
            extraction_model,
            response_format=python_code_and_logic_schema,
        )
        if response is None:
            return None, None
        llm_output = response.choices[0].message.content
        output_dict = get_dict_from_json_or_python(llm_output)
        return output_dict["code"], output_dict["logic"]
    except Exception as e:
        if chat_log is None:
            print(f"Error in llm_extract_python_code_and_logic: {traceback.format_exc()}")
            return None, None
        chat_log.error(f"Error in llm_extract_python_code_and_logic: {traceback.format_exc()}")
        return None, None


def get_post_process_params(text):
    packages = []
    inputs = []
    task = ""
    type = ""

    # Split the text into lines for easier processing
    lines = text.split("\n")

    # Process each line
    for line in lines:
        # Check if the line starts with "PACKAGES:" and extract package names
        if line.startswith("PACKAGES:"):
            packages_part = line.replace("PACKAGES:", "").strip()  # Remove "PACKAGES:" and strip whitespace
            packages = [
                pkg.strip() for pkg in packages_part.split(",") if pkg
            ]  # Split by comma and strip each package name

        # Check if the line starts with "INPUTS:" and extract inputs
        # elif line.startswith("INPUTS:"):
        #     inputs_part = line.replace("INPUTS:", "").strip()  # Remove "INPUTS:" and strip whitespace
        #     input_items = [inp.strip() for inp in inputs_part.split(",") if inp]  # Split by comma and strip

        #     # Process each input item to split into name and description
        #     for item in input_items:
        #         name, description = [x.strip() for x in item.split("-", 1)]  # Split by the first hyphen and strip
        #         inputs.append({"name": name, "description": description})
        elif line.startswith("TASK:"):
            task = line.replace("TASK:", "").strip()

        elif line.startswith("TYPE:"):
            type = line.replace("TYPE:", "").strip()  # Remove "TASK:" and strip whitespace

    return packages, type, task


"""
description:Function to convert a PNG file to a base64 string and format it for embedding in Markdown             
parameter  :
            file_path   : file path of the image
return: 
                            base64 format of the image            
"""


def png_to_base64_markdown_format(file_path):
    with open(file_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
        markdown_image_format = f"![](data:image/png;base64,{encoded_string})"
        return markdown_image_format


"""
description:Function to check table is given as a answer at anypoint in LLM chain of thought            
parameter  :
            text   :  LLM or input text which is to be checked which includes the table
return: 
                            True or False : True if table is given as a answer            
"""


def table_finish_check(text):
    # match = re.search(r"```(.*?)```", text, re.DOTALL)
    return "TABLE:" in text
    # if "TABLE" in text:
    # extracted_content = match.group(1).strip() if match else None
    # if extracted_content is not None and len(extracted_content)>0:
    #     return True
    # else:
    #     return False


"""
description:Function to check make table text into the format needed by the frontend            
parameter  :
            text          :  LLM or input text which is to be checked which includes the table
return: 
                                stream output of the tabletext which needed by the frontend           
"""


def table_stream(text):
    match = re.search(r"TABLE(:\s*)?(.*)", text, re.DOTALL)
    extracted_content = match.group(2).strip() if match else None
    # visible_newlines = extracted_content.replace("\n", "\n")
    result = f"\n{extracted_content}"
    return result


"""
description:Function to sleep the process after some text        
parameter  :
            text        :  LLM or input text which is to be checked
return: 
                            no return          
"""


def sleep_after_text(text):
    length = len(text)
    sleep_time = 0.015 * length + 0.2
    time.sleep(sleep_time)


def contains_urls(text):
    # Define a regular expression pattern to match URLs
    url_pattern = r"https?://\S+"
    match = re.search(url_pattern, text)
    return match is not None


def markdown_urls(input_text):

    def url_to_markdown(match):
        url = match.group()
        return f"[{url}]({url})"

    return re.sub(r"https?://\S+", url_to_markdown, input_text)


"""
description: Function to check the file extension and identify if it is a image or other file     
parameter  :
            extension :  case insensitive extension
return: 
        True if image file, False if it is a another file (csv, pdf, txt)         
"""


def check_image(extension):

    if extension.lower() in IMAGE_EXTENSIONS:
        return True
    elif extension.lower() in FILE_EXTENSIONS:
        return False


def remove_keyword_sections_from_str(str, start_keyword, end_keyword=None):
    """
    description: Remove all the characters between and including start and end keywords given. If end_keyword is not given, then only the start_keyword part is removed.
    """
    if start_keyword in str and (end_keyword is None or end_keyword in str):
        if end_keyword:
            pattern_full = rf"{start_keyword}.*?{end_keyword}"
            modified_str = re.sub(pattern_full, "", str)
        else:
            pattern_partial = rf"{start_keyword}"
            modified_str = re.sub(pattern_partial, "", str)
        return modified_str
    return str


def get_dict_from_json_or_python(input_str):

    # return -1 for None or empty array
    if input_str is None or input_str == []:
        return -1
    # First try simply parse the string as JSON
    try:
        dict_out = json.loads(input_str)
        return dict_out
    except Exception as e:
        json_string = input_str
        pattern_json = r"```json(.*?)```"
        pattern_python = r"```python(.*?)```"
        try:
            matches_json = re.findall(pattern_json, input_str, re.DOTALL)
            matches_python = re.findall(pattern_python, input_str, re.DOTALL)
            if matches_json:
                json_string = "\n".join(matches_json).strip("` \n")
            elif matches_python:
                json_string = "\n".join(matches_python).strip("` \n")
            else:
                json_string = input_str.strip("` \n")
            dict_out = json.loads(json_string)
            return dict_out
        except Exception as e:
            try:
                return eval(json_string)
            except Exception as e1:
                return -1


def truncate_data(data, truncate_length=3):
    """
    Function to truncate each list in a dictionary to a given length
    and format the dictionary for display.

    :param data: Dictionary with lists to be truncated
    :param truncate_length: Number of items to display from each list
    :return: A new dictionary with truncated lists and formatted entries
    """
    truncated_data = {}

    for key, value in data.items():
        # Truncate the list to the specified length and keep only the first `truncate_length` items
        truncated_value = value[:truncate_length]

        # Add a comment to indicate the truncation
        truncated_data[key] = [f"{item}" for item in truncated_value]  # Optionally, format as a string

    # Add ellipsis to indicate truncation for display
    truncated_data = {
        key: [f"{item} ..." if i == len(truncated_value) - 1 else item for i, item in enumerate(truncated_value)]
        for key, truncated_value in truncated_data.items()
    }

    return truncated_data


def array_to_markdown(array, topic):  # in not anything in array  return empty string
    if not array:
        return ""
    md = f"**{topic}**\n\n"
    for i, item in enumerate(array, 1):
        lines = item.strip().split("\n")
        if len(lines) == 1:
            md += f"{i}. {lines[0]}\n\n"
        else:
            # First line as main point, rest indented
            md += f"{i}. **{lines[0]}**\n"
            for subline in lines[1:]:
                if subline.strip():
                    md += f"   {subline}\n"
            md += "\n"
    return md


def handle_ai_exception(e: Exception, logger: Logger):
    """
    Handles exceptions thrown by AI (eg: openAI API)

    Args:
        e (Exception): The exception to be handled.

    Returns:
        tuple<bool, string>:  True if this exception needs to be raised upstream, False otherwise, Message to return to user
    """
    logger.error(f"Error with AI provider: {e}:\n{traceback.format_exc()}")
    is_raise = False
    if isinstance(e, RateLimitError):
        error_msg = "OpenAI rate limit exceeded. Please try again later."
        is_raise = True
    elif isinstance(e, OpenAIError) and "quota" in str(e).lower():
        error_msg = "OpenAI quota exceeded."
        is_raise = True
    elif isinstance(e, AuthenticationError):
        error_msg = "OpenAI authentication error."
        is_raise = True
    elif isinstance(e, APIError):
        error_msg = "OpenAI API error."
        is_raise = False
    elif isinstance(e, InternalServerError):
        error_msg = "OpenAI internal server error."
        is_raise = False
    else:
        error_msg = "Error with AI provider."
        is_raise = False

    e.message = error_msg
    return is_raise, error_msg


def apply_business_rules(business_rules, instructions):
    if business_rules:
        business_rules_instructions = ""
        with open("instructions/instruction_to_use_business_rules.txt", "r", encoding="utf-8") as file:
            business_rules_instructions = str(file.read())
        business_rules_instructions = business_rules_instructions.replace("<<BUSINESS_RULES>>", business_rules)

        instructions = instructions.replace(
            "<<INSTRUCTIONS_ON_BUSINESS_SPECIFIC_RULES>>",
            business_rules_instructions,
        )

    else:
        instructions = instructions.replace("<<INSTRUCTIONS_ON_BUSINESS_SPECIFIC_RULES>>", "")
    return instructions


response_analyzer_response_schema = {
    "name": "ResponseAnalyzerResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "feedback": {"type": "string"},
            "new_issues_detected": {"type": "boolean"},
            "is_issue_fixed": {"type": "boolean"},
            "retrieval_logic_corrected": {"type": "boolean"},
            "data_validated": {"type": "boolean"},
            "is_data_returned": {"type": "boolean"},
            "is_data_similar_to_previous": {"type": "boolean"},
        },
        "required": [
            "feedback",
            "new_issues_detected",
            "is_issue_fixed",
            "retrieval_logic_corrected",
            "data_validated",
            "is_data_returned",
            "is_data_similar_to_previous",
        ],
        "additionalProperties": False,
    },
}


data_review_response_schema = {
    "name": "DataReviewResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "feedback": {"type": "string"},
            "is_revision_required": {"type": "boolean"},
            "is_query_issues": {"type": "boolean"},
            "is_data_returned": {"type": "boolean"},
        },
        "required": [
            "feedback",
            "is_revision_required",
            "is_query_issues",
            "is_data_returned",
        ],
        "additionalProperties": False,
    },
}

sql_output_data_review_response_schema = {
    "name": "SQLOutputDataReviewResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "feedback": {"type": "string"},
            "is_revision_required_for_SQL": {"type": "boolean"},
        },
        "required": [
            "feedback",
            "is_revision_required_for_SQL",
        ],
        "additionalProperties": False,
    },
}

python_output_data_review_response_schema = {
    "name": "PythonOutputDataReviewResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {"feedback": {"type": "string"}, "is_revision_required": {"type": "boolean"}},
        "required": [
            "feedback",
            "is_revision_required",
        ],
        "additionalProperties": False,
    },
}

visualization_review_response_schema = {
    "name": "VisualizationReviewResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "feedback": {"type": "string"},
            "is_revision_required": {"type": "boolean"},
            "is_visualization_issue": {"type": "boolean"},
            "issue_severity": {"type": "string", "enum": ["CRITICAL", "NON_CRITICAL", "NONE"]},
        },
        "required": ["feedback", "is_revision_required", "is_visualization_issue", "issue_severity"],
        "additionalProperties": False,
    },
}


user_question_clarification_schema = {
    "name": "LLMClarificationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "clarification_needed": {"type": "boolean"},
            "uncertainty_reason": {"type": "string"},
            "clarification_question": {"type": "string"},
            "clarification_options": {"type": "array", "items": {"type": "string"}},
            "assumed_defaults": {"type": "string"},
        },
        "required": [
            "clarification_needed",
            "uncertainty_reason",
            "clarification_question",
            "clarification_options",
            "assumed_defaults",
        ],
        "additionalProperties": False,
    },
}


user_question_refinement_schema = {
    "name": "UserQuestionRefinementResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "refined_question": {"type": "string", "description": "The question after being refined by the LLM."},
            "clarifications_used": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Clarifications that were used to refine the question.",
            },
            "adjustments_made": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Changes made to align with business rules or policies.",
            },
            "final_assumptions": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Final assumptions made by the LLM during processing.",
            },
        },
        "required": ["refined_question", "clarifications_used", "adjustments_made", "final_assumptions"],
        "additionalProperties": False,
    },
}

data_identification_schema = {
    "name": "DataIdentificationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "data_retrieval_logic": {"type": "string"},
            "required_sections": {"type": "array", "items": {"type": "string"}},
            "reasoning": {"type": "string"},
            "assumptions": {"type": "string"},
            "is_data_available": {"type": "boolean"},
        },
        "required": ["data_retrieval_logic", "required_sections", "reasoning", "assumptions", "is_data_available"],
        "additionalProperties": False,
    },
}


data_validation_schema = {
    "name": "DataValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "key_findings": {"type": "string"},
            "furtherAnalysis": {
                "type": "object",
                "properties": {"required": {"type": "boolean"}, "data_retrieval_instructions": {"type": "string"}},
                "required": ["required", "data_retrieval_instructions"],
                "additionalProperties": False,
            },
            "is_data_valid": {"type": "boolean"},
        },
        "required": ["key_findings", "furtherAnalysis", "is_data_valid"],
        "additionalProperties": False,
    },
}


insight_thought_response_schema = {
    "name": "InsightThoughtResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "title_of_thoughts": {"type": "string"},
            "thought_reasoning": {"type": "string"},
            "retrieval_instruction": {"type": "string"},
            "is_conclusive": {"type": "boolean"},
            "analysis_objective": {"type": "string"},
            "relevant_tables": {"type": "array", "items": {"type": "string"}},
        },
        "required": [
            "title_of_thoughts",
            "thought_reasoning",
            "retrieval_instruction",
            "is_conclusive",
            "analysis_objective",
            "relevant_tables",
        ],
        "additionalProperties": False,
    },
}

insight_initial_thought_response_schema = {
    "name": "InsightInitialThoughtResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "title_of_thoughts": {"type": "string"},
            "thought_reasoning": {"type": "string"},
            "retrieval_instruction": {"type": "string"},
            "analysis_objective": {"type": "string"},
        },
        "required": [
            "title_of_thoughts",
            "thought_reasoning",
            "retrieval_instruction",
            "analysis_objective",
        ],
        "additionalProperties": False,
    },
}


insight_initial_analysis_schema = {
    "name": "InsightInitialAnalysisResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "insight": {"type": "string"},
            "is_useful": {"type": "boolean"},
            "hypothesis_insight": {"type": "string"},
            "is_quantitative_analysis_required": {"type": "boolean"},
            "quantitative_analysis_instruction": {"type": "string"},
            "is_data_transformation_required": {"type": "boolean"},
            "data_transformation_instruction": {"type": "string"},
            "data_nature_keyword": {
                "type": "string",
                "enum": [
                    "retrieval_failed",
                    "fully_relevant",
                    "partially_relevant",
                    "misaligned_or_insufficient",
                ],
            },
        },
        "required": [
            "insight",
            "is_useful",
            "hypothesis_insight",
            "is_quantitative_analysis_required",
            "quantitative_analysis_instruction",
            "is_data_transformation_required",
            "data_transformation_instruction",
            "data_nature_keyword",
        ],
        "additionalProperties": False,
    },
}


thought_step1_hypothesis_evaluation_schema = {
    "name": "ThoughtStep1HypothesisEvaluationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "evaluation_status": {"type": "string", "enum": ["conclusive", "inconclusive_but_viable", "infeasible"]},
            "hypothesis_support": {"type": "string", "enum": ["supported", "refuted", "uncertain"]},
            "summary_reasoning": {"type": "string"},
            "unexplored_angles": {"type": "array", "items": {"type": "string"}},
            "failed_data_patterns": {"type": "array", "items": {"type": "string"}},
            "deeproot_recommended": {"type": "boolean"},
            "deeproot_reasoning": {"type": "string"},
        },
        "required": [
            "evaluation_status",
            "hypothesis_support",
            "summary_reasoning",
            "unexplored_angles",
            "failed_data_patterns",
            "deeproot_recommended",
            "deeproot_reasoning",
        ],
        "additionalProperties": False,
    },
}


thought_step2_next_step_analysis_schema = {
    "name": "ThoughtStep2NextStepAnalysisResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "thought_reasoning": {"type": "string"},
            "is_analysis_complete": {"type": "boolean"},
            "title_of_thoughts": {"type": "string"},
            "retrieval_instruction": {"type": "string"},
            "analysis_objective": {"type": "string"},
            "relevant_tables": {"type": "array", "items": {"type": "string"}},
        },
        "required": [
            "thought_reasoning",
            "is_analysis_complete",
            "title_of_thoughts",
            "retrieval_instruction",
            "analysis_objective",
            "relevant_tables",
        ],
        "additionalProperties": False,
    },
}


insight_quantitative_analysis_schema = {
    "name": "InsightQuantitativeAnalysisResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "updated_insight": {"type": "string"},
        },
        "required": [
            "updated_insight",
        ],
        "additionalProperties": False,
    },
}


insight_identification_schema = {
    "name": "InsightIdentificationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "insights": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "insight_description": {"type": "string"},
                        "observation_keys": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["insight_description", "observation_keys"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["insights"],
        "additionalProperties": False,
    },
}
intern_report_schema_old = {
    "name": "InternReportResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "conclusion": {"type": "string"},
            "hypothesis_status": {"type": "string", "enum": ["proved", "disproved", "no_data"]},
            "data_points": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "data_point_id": {"type": "string"},
                        "is_key_fact": {"type": "boolean"},
                        "interpretation": {"type": "string"},
                        "finding": {"type": "string"},
                        "impact_on_hypothesis": {"type": "string"},
                        "observation_key_list": {"type": "array", "items": {"type": "string"}},
                    },
                    "required": [
                        "data_point_id",
                        "is_key_fact",
                        "interpretation",
                        "finding",
                        "impact_on_hypothesis",
                        "observation_key_list",
                    ],
                    "additionalProperties": False,
                },
            },
            "appendix_points": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "data_point_id": {"type": "string"},
                        "description": {"type": "string"},
                        "observation_key_list": {"type": "array", "items": {"type": "string"}},
                    },
                    "required": ["data_point_id", "description", "observation_key_list"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["conclusion", "hypothesis_status", "data_points", "appendix_points"],
        "additionalProperties": False,
    },
}


visualization_items_schema = {
    "name": "VisualizationItemsResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "type": {"type": "string", "enum": ["image", "table"]},
                        "header": {"type": "string"},
                        "src": {"type": "string"},
                    },
                    "required": ["type", "header", "src"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["items"],
        "additionalProperties": False,
    },
}

doc_summary_schema = {
    "name": "DocumentExtraction",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "document_overview": {
                "type": "object",
                "properties": {
                    "title": {"type": ["string", "null"], "maxLength": 200},
                    "description": {
                        "type": "string",
                        "minLength": 1,
                        "maxLength": 300,
                        # (Optional) If you want to constrain to ~1–2 sentences, add:
                        # "pattern": r"^([^.!?]+[.!?])( [^.!?]+[.!?])?$"
                    },
                    "pages": {"type": "integer", "minimum": 0},
                },
                "required": ["title", "description", "pages"],
                "additionalProperties": False,
            },
            "pages": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "page_number": {"type": "integer", "minimum": 1},
                        "elements": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "type": {
                                        "type": "string",
                                        "enum": ["table", "text_block", "list", "key_value_group"],
                                    },
                                    "title": {"type": "string", "minLength": 1, "maxLength": 200},
                                    "confidence": {"type": "number", "minimum": 0.0, "maximum": 1.0},
                                    "text_block": {
                                        "type": "object",
                                        "properties": {
                                            "word_count_estimate": {"type": "integer", "minimum": 0},
                                            "sample_text": {
                                                "type": "string",
                                                "maxLength": 200,
                                                # (Optional) approximate ≤20 words:
                                                # "pattern": r"^(?:\S+(?:\s+\S+){0,19})?$"
                                            },
                                        },
                                        "required": ["word_count_estimate", "sample_text"],
                                        "additionalProperties": False,
                                    },
                                    "list": {
                                        "type": "object",
                                        "properties": {
                                            "item_count_estimate": {"type": "integer", "minimum": 0},
                                            "sample_items": {
                                                "type": "array",
                                                "items": {"type": "string", "maxLength": 30},
                                                "minItems": 0,
                                                "maxItems": 3,
                                            },
                                        },
                                        "required": ["item_count_estimate", "sample_items"],
                                        "additionalProperties": False,
                                    },
                                    "key_value_group": {
                                        "type": "object",
                                        "properties": {
                                            "pair_count_estimate": {"type": "integer", "minimum": 0},
                                            "sample_pairs": {
                                                "type": "array",
                                                "items": {
                                                    "type": "object",
                                                    "properties": {
                                                        "key": {"type": "string", "maxLength": 100},
                                                        "value": {"type": "string", "maxLength": 30},
                                                    },
                                                    "required": ["key", "value"],
                                                    "additionalProperties": False,
                                                },
                                            },
                                        },
                                        "required": ["pair_count_estimate", "sample_pairs"],
                                        "additionalProperties": False,
                                    },
                                    "table": {
                                        "type": "object",
                                        "properties": {
                                            "row_count_estimate": {"type": "integer", "minimum": 0},
                                            "column_count_estimate": {"type": "integer", "minimum": 0},
                                            "large_table": {"type": "boolean"},
                                            "column_names": {
                                                "type": "array",
                                                "items": {"type": "string", "maxLength": 100},
                                                "minItems": 0,
                                            },
                                            "sample_rows": {
                                                "type": "array",
                                                "items": {
                                                    "type": "object",
                                                    "properties": {
                                                        "cells": {
                                                            "type": "array",
                                                            "items": {"type": "string", "maxLength": 30},
                                                            "minItems": 0,
                                                        }
                                                    },
                                                    "required": ["cells"],
                                                    "additionalProperties": False,
                                                },
                                            },
                                        },
                                        "required": [
                                            "row_count_estimate",
                                            "column_count_estimate",
                                            "large_table",
                                            "column_names",
                                            "sample_rows",
                                        ],
                                        "additionalProperties": False,
                                    },
                                },
                                "required": ["type", "title", "confidence"],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": ["page_number", "elements"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["document_overview", "pages"],
        "additionalProperties": False,
    },
}

extraction_result_schema = {
    "name": "DataExtractionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "columns": {
                "type": "array",
                "items": {"type": "string"},
            },
            "rows": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "string"},
                },
            },
            "file_name": {"type": "string", "description": "Name of the output CSV file"},
        },
        "required": ["columns", "rows", "file_name"],
        "additionalProperties": False,
    },
}


sample_data_schema = {
    "name": "SampleDataResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "sql_query_list": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {"table_name": {"type": "string"}, "sql_query": {"type": "string"}},
                    "required": ["table_name", "sql_query"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["sql_query_list"],
        "additionalProperties": False,
    },
}

sample_data_check_schema = {
    "name": "SampleDataCheckResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "data_match": {"type": "boolean"},
            "data_match_reason": {"type": "string"},
            "schema_match": {"type": "boolean"},
            "schema_match_reason": {"type": "string"},
            "additional_required_tables": {"type": "array", "items": {"type": "string"}},
            "updated_data_retrieval_plan": {"type": "string"},
            "correct_data_retrieval_plan": {"type": "boolean"},
        },
        "required": [
            "data_match",
            "data_match_reason",
            "schema_match",
            "schema_match_reason",
            "additional_required_tables",
            "updated_data_retrieval_plan",
            "correct_data_retrieval_plan",
        ],
        "additionalProperties": False,
    },
}


foreign_key_check_query_schema = {
    "name": "ForeignKeyCheckResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "analysis_results": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "analysis_name": {"type": "string"},
                        "description": {"type": "string"},
                        "tables_involved": {
                            "type": "object",
                            "properties": {
                                "child_table": {"type": "string"},
                                "parent_table": {"type": "string"},
                                "foreign_key": {"type": "string"},
                                "primary_key": {"type": "string"},
                                "table": {"type": "string"},
                            },
                            "required": ["child_table", "parent_table", "foreign_key", "primary_key", "table"],
                            "additionalProperties": False,
                        },
                        "sql_query": {"type": "string"},
                    },
                    "required": ["analysis_name", "description", "tables_involved", "sql_query"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["analysis_results"],
        "additionalProperties": False,
    },
}


foreign_key_evaluation_schema = {
    "name": "ForeignKeyEvaluationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "query_evaluation": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "analysis_name": {"type": "string"},
                        "status": {"type": "string", "enum": ["PASS", "FAIL"]},
                        "description": {"type": "string"},
                        "recommendations": {"type": "string"},
                    },
                    "required": ["analysis_name", "status", "description", "recommendations"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["query_evaluation"],
        "additionalProperties": False,
    },
}


foreign_key_validation_schema = {
    "name": "QueryEvaluationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "query_evaluation": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "analysis_name": {"type": "string"},
                        "tables_involved": {
                            "type": "object",
                            "properties": {
                                "child_table": {"type": "string"},
                                "parent_table": {"type": "string"},
                                "foreign_key": {"type": "string"},
                                "primary_key": {"type": "string"},
                                "table": {"type": "string"},
                            },
                            "required": [
                                "child_table",
                                "parent_table",
                                "foreign_key",
                                "primary_key",
                                "table",
                            ],
                            "additionalProperties": False,
                        },
                        "status": {"type": "string", "enum": ["PASS", "FAIL"]},
                        "description": {"type": "string"},
                        "recommendations": {"type": "string"},
                    },
                    "required": ["analysis_name", "tables_involved", "status", "description", "recommendations"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["query_evaluation"],
        "additionalProperties": False,
    },
}

null_analysis_query_schema = {
    "name": "NullAnalysisQueriesResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "null_analysis_queries": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "query_name": {"type": "string"},
                        "description": {"type": "string"},
                        "table": {"type": "string"},
                        "sql_query": {"type": "string"},
                    },
                    "required": ["query_name", "description", "table", "sql_query"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["null_analysis_queries"],
        "additionalProperties": False,
    },
}


query_construction_schema = {
    "name": "QueryConstructionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "step": {"type": "string"},
            "sql_query": {"type": "string"},
            "assumptions": {"type": "string"},
            "uncertainties": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "uncertainty": {"type": "string"},
                        "assumption": {"type": "string"},
                    },
                    "required": ["uncertainty", "assumption"],
                    "additionalProperties": False,
                },
            },
            "business_logics": {
                "type": "array",
                "items": {"type": "string"},
            },
            "error_handling": {
                "type": "object",
                "properties": {
                    "error_detected": {"type": "boolean"},
                    "error_message": {"type": "string"},
                    "corrected_query": {"type": "string"},
                },
                "required": ["error_detected", "error_message", "corrected_query"],
                "additionalProperties": False,
            },
            "updated_data_retrieval_plan": {"type": "string"},
            "final_step": {"type": "boolean"},
        },
        "required": [
            "step",
            "sql_query",
            "assumptions",
            "uncertainties",
            "business_logics",
            "error_handling",
            "updated_data_retrieval_plan",
            "final_step",
        ],
        "additionalProperties": False,
    },
}

data_availability_query_schema = {
    "name": "DiagnosticQueriesResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "diagnostic_queries": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "integer"},
                        "description": {"type": "string"},
                        "sql_query": {"type": "string"},
                    },
                    "required": ["step", "description", "sql_query"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["diagnostic_queries"],
        "additionalProperties": False,
    },
}

data_availability_analysis_schema = {
    "name": "DataAvailabilityCheckResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "data_unavailability_detected": {"type": "boolean"},
            "data_unavailability_reason": {"type": "string"},
            "query_formulation_issue_detected": {"type": "boolean"},
            "query_formulation_issue_reason": {"type": "string"},
            "additional_notes": {"type": "string"},
        },
        "required": [
            "data_unavailability_detected",
            "data_unavailability_reason",
            "query_formulation_issue_detected",
            "query_formulation_issue_reason",
            "additional_notes",
        ],
        "additionalProperties": False,
    },
}


query_debug_schema = {
    "name": "QueryDebugResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "errors_detected": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "error_type": {
                            "type": "string",
                            "description": "Type of error (e.g., Syntax, Field Error, Data Processing)",
                        },
                        "error_details": {"type": "string", "description": "Detailed explanation of the error"},
                        "suggested_fix": {
                            "type": "string",
                            "description": "Correction or alternative approach when an exact fix is not possible",
                        },
                    },
                    "required": ["error_type", "error_details", "suggested_fix"],
                    "additionalProperties": False,
                },
            },
            "additional_required_tables": {
                "type": "array",
                "items": {"type": "string", "description": "Potentially missing tables required to correct the query"},
                "description": "List of missing tables required to correct the query. If no additional tables are needed, this should be an empty array.",
            },
        },
        "required": ["errors_detected", "additional_required_tables"],
        "additionalProperties": False,
    },
}

insight_summarization_schema = {
    "name": "FindingsSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "findings": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "description": {"type": "string"},
                        "is_key": {"type": "boolean"},
                    },
                    "required": ["id", "description", "is_key"],
                    "additionalProperties": False,
                },
                "minItems": 1,
            }
        },
        "required": ["findings"],
        "additionalProperties": False,
    },
}


visualization_ordered_items_schema = {
    "name": "VisualizationItemsResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "type": {"type": "string", "enum": ["image", "table"]},
                        "src": {"type": "string"},
                    },
                    "required": ["type", "src"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["items"],
        "additionalProperties": False,
    },
}

query_knowledge_match_schema = {
    "name": "QueryMatchSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "query": {"type": "string"},
            "matched_nodes": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "entity": {"type": "string"},
                        "analysis_patterns": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "pattern_key": {"type": "string"},
                                    "pattern": {"type": "string"},
                                    "relevance": {"type": "string", "enum": ["direct", "indirect"]},
                                    "reason": {"type": "string"},
                                },
                                "required": ["pattern_key", "pattern", "relevance", "reason"],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": ["entity", "analysis_patterns"],
                    "additionalProperties": False,
                },
            },
            "notes": {"type": "string"},
        },
        "required": ["query", "matched_nodes", "notes"],
        "additionalProperties": False,
    },
}

intern_report_schema = {
    "name": "InternReportResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "hypothesis_evaluation": {
                "type": "object",
                "properties": {
                    "hypothesis": {"type": "string", "description": "The original hypothesis being evaluated."},
                    "evaluation_result": {
                        "type": "string",
                        "enum": ["Supported", "Refuted", "Inconclusive"],
                        "description": "Outcome of the evaluation.",
                    },
                    "reasoning": {
                        "type": "string",
                        "description": "Explanation of the reasoning behind the evaluation result.",
                    },
                },
                "required": ["hypothesis", "evaluation_result", "reasoning"],
                "additionalProperties": False,
            },
            "key_observations": {
                "type": "array",
                "description": "Observations directly contributing to the hypothesis evaluation.",
                "items": {
                    "type": "object",
                    "properties": {
                        "observation_key": {"type": "string"},
                        "title": {"type": "string"},
                        "retrieval_stage": {"type": "string", "enum": ["foundational", "causal", "correlative"]},
                        "data_retrieval_objective": {"type": "string"},
                        "causal_link_to_previous": {
                            "type": "object",
                            "properties": {
                                "is_available": {
                                    "type": "boolean",
                                    "description": "Whether the causal link is available or not.",
                                },
                                "derived_from": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "List of observation keys that this observation is derived from. Keep this empty if is_available is false.",
                                },
                                "explanation": {
                                    "type": "string",
                                    "description": "Keep this empty if is_available is false.",
                                },
                            },
                            "required": ["derived_from", "explanation", "is_available"],
                            "additionalProperties": False,
                        },
                        "insight": {"type": "string"},
                        "impact_on_hypothesis": {
                            "type": "object",
                            "properties": {
                                "result": {"type": "string", "enum": ["Supports", "Refutes", "Neutral"]},
                                "explanation": {"type": "string"},
                            },
                            "required": ["result", "explanation"],
                            "additionalProperties": False,
                        },
                    },
                    "required": [
                        "observation_key",
                        "title",
                        "retrieval_stage",
                        "data_retrieval_objective",
                        "insight",
                        "impact_on_hypothesis",
                        "causal_link_to_previous",
                    ],
                    "additionalProperties": False,
                },
            },
            "appendix_observations": {
                "type": "array",
                "description": "Supporting observations that do not directly impact the hypothesis.",
                "items": {
                    "type": "object",
                    "properties": {
                        "observation_key": {"type": "string"},
                        "title": {"type": "string"},
                        "summary": {"type": "string"},
                        "reason_in_appendix": {"type": "string"},
                    },
                    "required": ["observation_key", "title", "summary", "reason_in_appendix"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["hypothesis_evaluation", "key_observations", "appendix_observations"],
        "additionalProperties": False,
    },
}

insight_master_report_schema = {
    "name": "InsightReportResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "InsightReport": {
                "type": "object",
                "properties": {
                    "majorFinding": {"type": "string"},
                    "insights": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "summary": {"type": "string"},
                                "root_cause": {"type": "array", "items": {"type": "string"}},
                                "business_impact": {"type": "array", "items": {"type": "string"}},
                                "is_key_fact": {"type": "boolean"},
                                "hypothesis_data_points_list": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "hypothesis_id": {"type": "string"},
                                            "data_point_ids": {"type": "array", "items": {"type": "string"}},
                                        },
                                        "required": ["hypothesis_id", "data_point_ids"],
                                        "additionalProperties": False,
                                    },
                                },
                                "data_relevance": {"type": "string"},
                            },
                            "required": [
                                "summary",
                                "root_cause",
                                "business_impact",
                                "is_key_fact",
                                "hypothesis_data_points_list",
                                "data_relevance",
                            ],
                            "additionalProperties": False,
                        },
                    },
                    "actionableRecommendations": {
                        "type": "object",
                        "properties": {
                            "shortDescription": {"type": "string"},
                            "bulletPoints": {"type": "array", "items": {"type": "string"}},
                        },
                        "required": ["shortDescription", "bulletPoints"],
                        "additionalProperties": False,
                    },
                },
                "required": ["majorFinding", "insights", "actionableRecommendations"],
                "additionalProperties": False,
            }
        },
        "required": ["InsightReport"],
        "additionalProperties": False,
    },
}


thought_instruction_validation_schema = {
    "name": "ThoughtInstructionValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_instruction_valid": {"type": "boolean"},
            "modified_instruction": {
                "type": "string",
                "description": "Modified instruction to avoid data issues, or 'none' if not modified",
            },
            "is_data_available": {"type": "boolean"},
        },
        "required": ["is_instruction_valid", "modified_instruction", "is_data_available"],
        "additionalProperties": False,
    },
}


review_analysis_schema = {
    "name": "AssessmentValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "assessment": {
                "type": "object",
                "properties": {
                    "useful": {
                        "type": "boolean",
                    },
                    "reason": {
                        "type": "string",
                    },
                    "is_hypothetical_data_used": {
                        "type": "boolean",
                    },
                },
                "required": ["useful", "reason", "is_hypothetical_data_used"],
                "additionalProperties": False,
            },
        },
        "required": ["assessment"],
        "additionalProperties": False,
    },
}


thought_instruction_feedback_schema_v1 = {
    "name": "ThoughtInstructionValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_instruction_valid": {"type": "boolean"},
            "feedback": {
                "type": "string",
            },
            "is_data_available": {"type": "boolean"},
        },
        "required": ["is_instruction_valid", "feedback", "is_data_available"],
        "additionalProperties": False,
    },
}


pre_analysis_reasoning_schema = {
    "name": "AnalysisResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "phase": {"type": "string"},
            "action_mode": {"type": "string", "enum": ["dictionary_loading", "pre_data_analysis"]},
            "action_instruction": {"type": "string"},
            "required_sections": {
                "type": "array",
                "items": {"type": "string"},
            },
            "is_analysis_complete": {"type": "boolean"},
        },
        "required": ["phase", "action_mode", "action_instruction", "required_sections", "is_analysis_complete"],
        "additionalProperties": False,
    },
}

thought_instruction_feedback_schema = {
    "name": "InstructionValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_instruction_valid": {"type": "boolean"},
            "issues": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "category": {
                            "type": "string",
                            "enum": ["data_mismatch", "missing_context", "ambiguity", "excessive_scope"],
                        },
                        "description": {"type": "string"},
                    },
                    "required": ["category", "description"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["is_instruction_valid", "issues"],
        "additionalProperties": False,
    },
}

refine_thought_instruction_schema = {
    "name": "RefineThoughtInstructionSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {"refined_instruction": {"type": "string"}},
        "required": ["refined_instruction"],
        "additionalProperties": False,
    },
}


intern_report_accuracy_feedback = {
    "name": "ReportAccuracyFeedbackSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_report_accurate": {"type": "boolean", "description": "Indicates if the report is accurate or not"},
            "issues_found": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "type": {
                            "type": "string",
                            "description": "Type of issue found, e.g., 'Missing Insight', 'Fabricated Content'",
                        },
                        "location": {
                            "type": "string",
                            "description": "Location in the report where the issue was found",
                        },
                        "details": {"type": "string", "description": "Detailed explanation of the issue"},
                    },
                    "required": ["type", "location", "details"],
                    "additionalProperties": False,
                },
                "description": "List of issues identified in the report",
            },
            "summary_feedback": {
                "type": "string",
                "description": "Summary feedback about the report accuracy and issues",
            },
        },
        "required": ["is_report_accurate", "issues_found", "summary_feedback"],
        "additionalProperties": False,
    },
}

master_report_accuracy_feedback = {
    "name": "InsightReviewSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "review_summary": {
                "type": "object",
                "properties": {
                    "overall_quality": {"type": "string", "enum": ["High", "Moderate", "Low"]},
                    "issues_found": {"type": "boolean"},
                    "comments": {"type": "string"},
                },
                "required": ["overall_quality", "issues_found", "comments"],
                "additionalProperties": False,
            },
            "insight_reviews": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "insight_index": {"type": "integer"},
                        "summary_check": {"type": "boolean"},
                        "data_traceable": {"type": "boolean"},
                        "causal_claims_valid": {"type": "boolean"},
                        "hypothesis_alignment": {"type": "boolean"},
                        "speculative_content": {"type": "boolean"},
                        "completeness_check": {"type": "boolean"},
                        "critical_issue": {"type": "boolean"},
                        "comments": {"type": "string"},
                    },
                    "required": [
                        "insight_index",
                        "summary_check",
                        "data_traceable",
                        "causal_claims_valid",
                        "hypothesis_alignment",
                        "speculative_content",
                        "completeness_check",
                        "critical_issue",
                        "comments",
                    ],
                    "additionalProperties": False,
                },
            },
            "recommended_actions": {
                "type": "array",
                "items": {"type": "string"},
                "default": [],
            },
        },
        "required": ["review_summary", "insight_reviews", "recommended_actions"],
        "additionalProperties": False,
    },
}


key_findings_schema = {
    "name": "KeyFindingSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "summary": {
                "type": "string",
                "description": "One sentence that explains the goal of the hypothesis evaluation",
            },
            "key_findings": {
                "type": "array",
                "items": {"type": "string", "description": "Data-backed finding, max 35 words"},
                "minItems": 3,
                "maxItems": 4,
                "description": ("Array of findings: 3 mandatory findings and 1 optional if it adds distinct value"),
            },
        },
        "required": ["summary", "key_findings"],
        "additionalProperties": False,
    },
}


hypothesis_evaluation_schema_v1 = {
    "name": "HypothesisInsightEvaluationSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "hypothesis_evaluation": {
                "type": "object",
                "properties": {
                    "status_of_hypothesis": {"type": "string", "enum": ["supported", "refuted", "inconclusive"]},
                    "reasoning": {"type": "string"},
                },
                "required": ["status_of_hypothesis", "reasoning"],
                "additionalProperties": False,
            },
            "insight_importance": {
                "type": "object",
                "properties": {"is_key_insight": {"type": "boolean"}, "reasoning": {"type": "string"}},
                "required": ["is_key_insight", "reasoning"],
                "additionalProperties": False,
            },
            "summary_points": {
                "type": "array",
                "items": {"type": "string"},
            },
        },
        "required": ["hypothesis_evaluation", "insight_importance", "summary_points"],
        "additionalProperties": False,
    },
}

hypothesis_evaluation_schema = {
    "name": "InsightSummarySchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "insight_importance": {
                "type": "object",
                "properties": {
                    "is_key_insight": {"type": "boolean"},
                    "relevance_to_hypothesis": {"type": "boolean"},
                    "reasoning": {"type": "string"},
                },
                "required": ["is_key_insight", "relevance_to_hypothesis", "reasoning"],
                "additionalProperties": False,
            },
            "summary_points": {
                "type": "object",
                "properties": {
                    "new_points": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "point_key": {"type": "string"},
                                "observation_key": {"type": "string"},
                                "is_data_backed": {"type": "boolean"},
                                "insight": {"type": "string", "maxLength": 500},
                            },
                            "required": ["point_key", "observation_key", "is_data_backed", "insight"],
                            "additionalProperties": False,
                        },
                    },
                    "updated_points": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "point_key": {"type": "string"},
                                "insight": {"type": "string", "maxLength": 500},
                            },
                            "required": ["point_key", "insight"],
                            "additionalProperties": False,
                        },
                    },
                },
                "required": ["new_points", "updated_points"],
                "additionalProperties": False,
            },
        },
        "required": ["insight_importance", "summary_points"],
        "additionalProperties": False,
    },
}


relevant_labels_schema = {
    "name": "RelevantLabelsSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "relevant_labels": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "output_label": {"type": "string"},
                        "matching_labels": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["output_label", "matching_labels"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["relevant_labels"],
        "additionalProperties": False,
    },
}


output_labels_list_schema = {
    "name": "OutputLabelsListSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "relevant_labels": {
                "type": "array",
                "items": {"type": "string"},
            }
        },
        "required": ["relevant_labels"],
        "additionalProperties": False,
    },
}

label_filtering_schema = {
    "name": "LabelFilteringSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "label_indexes": {
                "type": "array",
                "items": {"type": "integer"},
            },
        },
        "required": ["label_indexes"],
        "additionalProperties": False,
    },
}

blockwise_filtering_schema = {
    "name": "BlockwiseFieldFilteringResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "table_name": {"type": "string", "description": "Name of the given table"},
            "required_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string", "description": "Name of the potentially relevant field"},
                        "reasoning": {
                            "type": "string",
                            "description": "Brief description of why this field is potentially relevant",
                        },
                        "uncertainty_flag": {
                            "type": "boolean",
                            "description": "Indicates if there is uncertainty about this field's relevance",
                        },
                        "uncertainty_reason": {
                            "type": "string",
                            "description": "Brief explanation of the uncertainty if any",
                        },
                    },
                    "required": ["field_name", "reasoning", "uncertainty_flag", "uncertainty_reason"],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {
                "type": "boolean",
                "description": "Indicates if the required data is available in the table",
            },
        },
        "required": ["table_name", "required_fields", "is_data_available"],
        "additionalProperties": False,
    },
}

topic_matching_schema = {
    "name": "TopicMatchingSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "label_indexes": {
                "type": "array",
                "items": {"type": "integer"},
            },
            "is_full_match": {"type": "boolean"},
            "output_label_mapping": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "output_label": {"type": "string"},
                        "matching_indexes": {
                            "type": "array",
                            "items": {"type": "integer"},
                        },
                    },
                    "required": ["output_label", "matching_indexes"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["label_indexes", "is_full_match", "output_label_mapping"],
        "additionalProperties": False,
    },
}

additional_labels_output_schema = {
    "name": "AdditionalLabelsOutputSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "new_labels": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "topic": {"type": "string"},
                        "labels": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["topic", "labels"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["new_labels"],
        "additionalProperties": False,
    },
}


reusable_labels_output_schema = {
    "name": "ReUsableLabelsOutputSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "labels_reused": {
                "type": "array",
                "items": {"type": "string"},
            },
        },
        "required": ["labels_reused"],
        "additionalProperties": False,
    },
}

labels_merge_schema = {
    "name": "LabelsMergeSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "duplicate_labels": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "duplicate_in": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                        "merged_label": {"type": "string"},
                    },
                    "required": ["duplicate_in", "merged_label"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["duplicate_labels"],
        "additionalProperties": False,
    },
}

_label_logic_schema = {
    "name": "LabelLogicSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "output_labels": {
                "type": "array",
                "items": {"type": "string"},
            },
            "logic_flow": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "OR_labels": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["OR_labels"],
                    "additionalProperties": False,
                },
            },
            "labels_to_merge": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "input_labels": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                        "output_label": {"type": "string"},
                    },
                    "required": ["input_labels", "output_label"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["output_labels", "logic_flow", "labels_to_merge"],
        "additionalProperties": False,
    },
}

label_combine_logic_schema = {
    "name": "LabelCombineLogicSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "logic_flow": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "AND": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["AND"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["logic_flow"],
        "additionalProperties": False,
    },
}

label_merge_logic_schema = {
    "name": "LabelMergeLogicSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "output_labels": {
                "type": "array",
                "items": {"type": "string"},
            },
            "labels_to_merge": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "input_labels": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                        "output_label": {"type": "string"},
                    },
                    "required": ["input_labels", "output_label"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["output_labels", "labels_to_merge"],
        "additionalProperties": False,
    },
}

insight_master_report_schema_1 = {
    "name": "InsightReportSchema",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "InsightReport": {
                "type": "object",
                "properties": {
                    "keyFindings": {
                        "type": "object",
                        "properties": {
                            "shortDescription": {"type": "string"},
                            "keyFacts": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "description": {"type": "string"},
                                        "observation_key_list": {"type": "array", "items": {"type": "string"}},
                                        "data_relevance": {"type": "string"},
                                    },
                                    "required": ["description", "observation_key_list", "data_relevance"],
                                    "additionalProperties": False,
                                },
                            },
                            "visualizations": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "title": {"type": "string"},
                                        "items": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "type": {"type": "string"},
                                                    "src": {"type": "string"},
                                                    "altText": {"type": "string"},
                                                    "data": {
                                                        "type": "object",
                                                        "properties": {
                                                            "title": {"type": "string"},
                                                            "headers": {
                                                                "type": "array",
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {"field": {"type": "string"}},
                                                                    "required": ["field"],
                                                                    "additionalProperties": False,
                                                                },
                                                            },
                                                            "rows": {
                                                                "type": "array",
                                                                "items": {
                                                                    "type": "array",
                                                                    "items": {"type": "string"},
                                                                },
                                                            },
                                                        },
                                                        "required": ["title", "headers", "rows"],
                                                        "additionalProperties": False,
                                                    },
                                                },
                                                "required": ["type", "src", "altText", "data"],
                                                "additionalProperties": False,
                                            },
                                        },
                                    },
                                    "required": ["title", "items"],
                                    "additionalProperties": False,
                                },
                            },
                        },
                        "required": ["shortDescription", "keyFacts", "visualizations"],
                        "additionalProperties": False,
                    },
                    "actionableRecommendations": {
                        "type": "object",
                        "properties": {
                            "shortDescription": {"type": "string"},
                            "bulletPoints": {"type": "array", "items": {"type": "string"}},
                        },
                        "required": ["shortDescription", "bulletPoints"],
                        "additionalProperties": False,
                    },
                    "otherFacts": {
                        "type": "object",
                        "properties": {"bulletPoints": {"type": "array", "items": {"type": "string"}}},
                        "required": ["bulletPoints"],
                        "additionalProperties": False,
                    },
                    "appendix": {
                        "type": "object",
                        "properties": {
                            "visualizations": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {"type": "string"},
                                        "src": {"type": "string"},
                                        "altText": {"type": "string"},
                                    },
                                    "required": ["type", "src", "altText"],
                                    "additionalProperties": False,
                                },
                            }
                        },
                        "required": ["visualizations"],
                        "additionalProperties": False,
                    },
                    "diagnosticAnalysis": {
                        "type": "object",
                        "properties": {
                            "insights": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "interpretation": {"type": "string"},
                                        "visualizations": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "type": {"type": "string"},
                                                    "src": {"type": "string"},
                                                    "altText": {"type": "string"},
                                                },
                                                "required": ["type", "src", "altText"],
                                                "additionalProperties": False,
                                            },
                                        },
                                    },
                                    "required": ["interpretation", "visualizations"],
                                    "additionalProperties": False,
                                },
                            }
                        },
                        "required": ["insights"],
                        "additionalProperties": False,
                    },
                },
                "required": [
                    "keyFindings",
                    "actionableRecommendations",
                    "otherFacts",
                    "appendix",
                    "diagnosticAnalysis",
                ],
                "additionalProperties": False,
            }
        },
        "required": ["InsightReport"],
        "additionalProperties": False,
    },
}


insight_query_schema = {
    "name": "DataValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {"insight_query": {"type": "string"}},
        "required": ["insight_query"],
        "additionalProperties": False,
    },
}

insight_board_value_extract_schema = {
    "name": "DataValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "current_value": {"type": "number"},
            "previous_value": {"type": "number"},
            "currency_sign": {"type": "string"},
            "statement": {"type": "string"},
        },
        "required": ["current_value", "previous_value", "currency_sign", "statement"],
        "additionalProperties": False,
    },
}

raw_data_table_selection_schema = {
    "name": "RawDataTableSelectionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "data_retrieval_logic": {"type": "string"},
            "required_tables": {"type": "array", "items": {"type": "string"}},
            "reasoning": {"type": "string"},
            "assumptions": {"type": "string"},
            "is_data_available": {"type": "boolean"},
        },
        "required": ["data_retrieval_logic", "required_tables", "reasoning", "assumptions", "is_data_available"],
        "additionalProperties": False,
    },
}


raw_data_field_selection_schema = {
    "name": "RawDataFieldSelectionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "data_retrieval_logic_feedback": {"type": "string"},
            "table_name": {"type": "string"},
            "required_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string"},
                        "reasoning": {"type": "string"},
                        "is_uncertain": {"type": "boolean"},
                        "uncertainty": {"type": "string"},
                        "assumption": {"type": "string"},
                    },
                    "required": ["field_name", "reasoning", "is_uncertain", "uncertainty", "assumption"],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {"type": "boolean"},
        },
        "required": [
            "data_retrieval_logic_feedback",
            "table_name",
            "required_fields",
            "is_data_available",
        ],
        "additionalProperties": False,
    },
}

cross_table_field_selection_schema_V1 = {
    "name": "CrossTableFieldSelectionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string"},
                        "fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string"},
                                    "uncertainty_flag": {"type": "boolean"},
                                    "uncertainty_type": {
                                        "type": "string",
                                        "enum": [
                                            "multi_field_conflict",
                                            "semantic_misalignment",
                                            "data_quality_concern",
                                            "business_rule_ambiguity",
                                            "none",
                                        ],
                                    },
                                    "uncertainty_reason": {"type": "string"},
                                },
                                "required": [
                                    "field_name",
                                    "uncertainty_flag",
                                    "uncertainty_type",
                                    "uncertainty_reason",
                                ],
                                "additionalProperties": False,
                            },
                        },
                        "reasoning": {"type": "string"},
                        "assumptions": {"type": "string"},
                        "is_data_available": {"type": "boolean"},
                        "is_uncertain": {"type": "boolean"},
                    },
                    "required": [
                        "table_name",
                        "fields",
                        "reasoning",
                        "assumptions",
                        "is_data_available",
                        "is_uncertain",
                    ],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["tables"],
        "additionalProperties": False,
    },
}

final_data_retrieval_plan_schema = {
    "name": "FinalDataRetrievalPlanResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "final_data_retrieval_plan": {"type": "string"},
            "required_tables_and_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string"},
                        "fields": {"type": "array", "items": {"type": "string"}},
                    },
                    "required": ["table_name", "fields"],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {"type": "boolean"},
        },
        "required": ["final_data_retrieval_plan", "required_tables_and_fields", "is_data_available"],
        "additionalProperties": False,
    },
}

cross_table_field_selection_schema_old = {
    "name": "FinalDataRetrievalPlanResponseV2",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "final_data_retrieval_plan": {
                "type": "string",
                "description": "Clear plan (max 50 words) describing how the answer will be derived",
            },
            "is_data_available": {"type": "boolean", "description": "Indicates if required data is available"},
            "selected_tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the selected table"},
                        "fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string", "description": "Name of the field"},
                                    "reasoning": {"type": "string", "description": "Why this field is necessary"},
                                    "uncertainty_flag": {
                                        "type": "boolean",
                                        "description": "Indicates if there is uncertainty about this field",
                                    },
                                    "uncertainty_type": {
                                        "type": "string",
                                        "description": "Type of uncertainty if uncertain",
                                        "enum": [
                                            "multi_field_conflict",
                                            "semantic_misalignment",
                                            "data_quality_concern",
                                            "business_rule_ambiguity",
                                            "none",
                                        ],
                                    },
                                    "uncertainty_reason": {
                                        "type": "string",
                                        "description": "Description of any lingering ambiguity",
                                    },
                                    "assumption": {
                                        "type": "string",
                                        "description": "Any assumption made in selecting this field",
                                    },
                                },
                                "required": [
                                    "field_name",
                                    "reasoning",
                                    "uncertainty_flag",
                                    "uncertainty_type",
                                    "uncertainty_reason",
                                    "assumption",
                                ],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": ["table_name", "fields"],
                    "additionalProperties": False,
                },
            },
            "discarded_tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the discarded table"},
                        "reason_for_removal": {
                            "type": "string",
                            "description": "Explanation for why the table was removed",
                        },
                        "was_initially_selected": {
                            "type": "boolean",
                            "description": "Whether the table was initially selected before being discarded",
                        },
                    },
                    "required": ["table_name", "reason_for_removal", "was_initially_selected"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["final_data_retrieval_plan", "is_data_available", "selected_tables", "discarded_tables"],
        "additionalProperties": False,
    },
}

cross_table_field_selection_and_query_plan_schema = {
    "name": "CrossTableFieldSelectionAndQueryPlanResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "final_SQL_Query_plan": {
                "type": "string",
                "description": "Clear plan (max 50 words) describing how the query will answer the question",
            },
            "is_data_available": {"type": "boolean", "description": "Indicates if required data is available"},
            "no_uncertainty": {
                "type": "boolean",
                "description": "Indicates if there is full confidence in the selected tables and columns",
            },
            "can_answer_with_uncertainty": {
                "type": "boolean",
                "description": "Indicates if a valid answer can be constructed despite some uncertainty",
            },
            "can_not_answer_with_certainty": {
                "type": "boolean",
                "description": "Indicates if uncertainties prevent reliably answering the question",
            },
            "reason": {"type": "string", "description": "Explanation for the confidence flags"},
            "table_join_strategy": {
                "type": "object",
                "properties": {
                    "description": {"type": "string", "description": "How tables should be joined and on what keys"},
                    "join_uncertainty_flag": {
                        "type": "boolean",
                        "description": "Indicates if there is uncertainty in the join logic",
                    },
                    "uncertainty_type": {
                        "type": "string",
                        "description": "Type of uncertainty if any",
                        "enum": [
                            "multi_field_conflict",
                            "semantic_misalignment",
                            "data_quality_concern",
                            "business_rule_ambiguity",
                            "none",
                        ],
                    },
                    "join_uncertainty_reason": {"type": "string", "description": "Explanation of any join ambiguity"},
                },
                "required": ["description", "join_uncertainty_flag", "uncertainty_type", "join_uncertainty_reason"],
                "additionalProperties": False,
            },
            "selected_tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the selected table"},
                        "fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string", "description": "Name of the selected field"},
                                    "reasoning": {"type": "string", "description": "Why this field is necessary"},
                                    "uncertainty_flag": {
                                        "type": "boolean",
                                        "description": "Indicates if there is uncertainty about this field",
                                    },
                                    "uncertainty_type": {
                                        "type": "string",
                                        "description": "Type of uncertainty if any",
                                        "enum": [
                                            "multi_field_conflict",
                                            "semantic_misalignment",
                                            "data_quality_concern",
                                            "business_rule_ambiguity",
                                            "none",
                                        ],
                                    },
                                    "uncertainty_reason": {
                                        "type": "string",
                                        "description": "Clear explanation of the issue",
                                    },
                                    "assumption": {
                                        "type": "string",
                                        "description": "Any assumption made to justify selection",
                                    },
                                },
                                "required": [
                                    "field_name",
                                    "reasoning",
                                    "uncertainty_flag",
                                    "uncertainty_type",
                                    "uncertainty_reason",
                                    "assumption",
                                ],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": ["table_name", "fields"],
                    "additionalProperties": False,
                },
            },
        },
        "required": [
            "final_SQL_Query_plan",
            "is_data_available",
            "no_uncertainty",
            "can_answer_with_uncertainty",
            "can_not_answer_with_certainty",
            "reason",
            "table_join_strategy",
            "selected_tables",
        ],
        "additionalProperties": False,
    },
}

query_plan_schema = {
    "name": "QueryPlanResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_data_available": {"type": "boolean"},
            "missing_data": {
                "type": "array",
                "items": {
                    "type": "string",
                    "description": "Detailed description (max. 40 words) of the each missing data item including what is missing and why it is required.",
                },
            },
            # "has_uncertainties": {"type": "boolean"},
            # "uncertainties": {
            #     "type": "array",
            #     "items": {
            #         "type": "object",
            #         "properties": {
            #             "reason": {"type": "string", "description": "Reason for the uncertainty in max. 40 words"},
            #             "uncertainty_table_fields": {
            #                 "type": "array",
            #                 "items": {
            #                     "type": "object",
            #                     "properties": {
            #                         "table_name": {"type": "string"},
            #                         "fields": {"type": "array", "items": {"type": "string"}},
            #                     },
            #                     "required": ["table_name", "fields"],
            #                     "additionalProperties": False,
            #                 },
            #             },
            #         },
            #         "required": ["reason", "uncertainty_table_fields"],
            #         "additionalProperties": False,
            #     },
            # },
            "relevant_table_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string"},
                        "fields": {"type": "array", "items": {"type": "string"}},
                    },
                    "required": ["table_name", "fields"],
                    "additionalProperties": False,
                },
            },
        },
        "required": [
            "is_data_available",
            "missing_data",
            # "has_uncertainties",
            # "uncertainties",
            "relevant_table_fields",
        ],
        "additionalProperties": False,
    },
}


table_selection_output_schema = {
    "name": "TableSelectionOutputResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "selected_tables": {
                "type": "array",
                "items": {
                    "type": "string",
                    "description": "Name of the selected table",
                },
            }
        },
        "required": ["selected_tables"],
        "additionalProperties": False,
    },
}


data_find_output_schema = {
    "name": "DataFindOutputResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "table_columns": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the table"},
                        "columns": {"type": "array", "items": {"type": "string", "description": "Name of the column"}},
                    },
                    "required": ["table_name", "columns"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["table_columns"],
        "additionalProperties": False,
    },
}

data_find_output_with_indexes_schema = {
    "name": "DataFindOutputResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "table_column_indices": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_index": {"type": "number", "description": "Index of the table"},
                        "column_indices": {
                            "type": "array",
                            "items": {"type": "number", "description": "Index of the column"},
                        },
                    },
                    "required": ["table_index", "column_indices"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["table_column_indices"],
        "additionalProperties": False,
    },
}

business_rule_output_schema = {
    "name": "BusinessRuleOutputResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {"business_rule_indices": {"type": "array", "items": {"type": "number"}}},
        "required": ["business_rule_indices"],
        "additionalProperties": False,
    },
}

sql_query_generate_schema = {
    "name": "SQLQueryGenerateResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_error": {"type": "boolean"},
            "error_message": {"type": "string"},
            "sql_query": {"type": "string"},
            "reasoning_as_bullet_points": {"type": "array", "items": {"type": "string"}},
            "data_file_names": {"type": "array", "items": {"type": "string"}},
        },
        "required": [
            "is_error",
            "error_message",
            "sql_query",
            "reasoning_as_bullet_points",
            "data_file_names",
        ],
        "additionalProperties": False,
    },
}

final_field_filtering_schema = {
    "name": "FinalFieldFilteringResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "table_name": {"type": "string", "description": "Name of the table"},
            "required_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string", "description": "Name of the field"},
                        "reasoning": {
                            "type": "string",
                            "description": "Why this field is necessary for answering the question",
                        },
                        "uncertainty_flag": {
                            "type": "boolean",
                            "description": "Indicates if there is uncertainty about this field",
                        },
                        "uncertainty_type": {
                            "type": "string",
                            "description": "Type of uncertainty (e.g., multi_field_conflict, semantic_misalignment)",
                            "enum": [
                                "multi_field_conflict",
                                "semantic_misalignment",
                                "data_quality_concern",
                                "business_rule_ambiguity",
                                "none",
                            ],
                        },
                        "uncertainty_reason": {
                            "type": "string",
                            "description": "Short explanation if uncertainty still exists",
                        },
                    },
                    "required": [
                        "field_name",
                        "reasoning",
                        "uncertainty_flag",
                        "uncertainty_type",
                        "uncertainty_reason",
                    ],
                    "additionalProperties": False,
                },
            },
            "filtered_out_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string", "description": "Name of the filtered out field"},
                        "was_uncertain": {
                            "type": "boolean",
                            "description": "Whether the field had uncertainty flags before removal",
                        },
                        "reason_for_removal": {
                            "type": "string",
                            "description": "Explanation for why the field was removed",
                        },
                    },
                    "required": ["field_name", "was_uncertain", "reason_for_removal"],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {"type": "boolean", "description": "Indicates if the required data is available"},
        },
        "required": [
            "table_name",
            "required_fields",
            "filtered_out_fields",
            "is_data_available",
        ],
        "additionalProperties": False,
    },
}

all_table_field_filtering_schema = {
    "name": "AllTableFieldFilteringResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the table"},
                        "required_fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string", "description": "Name of the field"},
                                    "reasoning": {
                                        "type": "string",
                                        "description": "Why this field is necessary for answering the question",
                                    },
                                    "uncertainty_flag": {
                                        "type": "boolean",
                                        "description": "Indicates if there is uncertainty about this field",
                                    },
                                    "uncertainty_type": {
                                        "type": "string",
                                        "description": "Type of uncertainty (e.g., multi_field_conflict, semantic_misalignment)",
                                        "enum": [
                                            "multi_field_conflict",
                                            "semantic_misalignment",
                                            "data_quality_concern",
                                            "business_rule_ambiguity",
                                            "none",
                                        ],
                                    },
                                    "uncertainty_reason": {
                                        "type": "string",
                                        "description": "Short explanation if uncertainty still exists",
                                    },
                                },
                                "required": [
                                    "field_name",
                                    "reasoning",
                                    "uncertainty_flag",
                                    "uncertainty_type",
                                    "uncertainty_reason",
                                ],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": [
                        "table_name",
                        "required_fields",
                    ],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {"type": "boolean", "description": "Indicates if the required data is available"},
        },
        "required": [
            "tables",
            "is_data_available",
        ],
        "additionalProperties": False,
    },
}

key_field_identify_schema = {
    "name": "KeyFieldIdentifyResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the table"},
                        "required_fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string", "description": "Name of the column"},
                                    "reasoning": {
                                        "type": "string",
                                        "description": "Why this column is necessary as key for answering the question",
                                    },
                                    "uncertainty_flag": {
                                        "type": "boolean",
                                        "description": "Indicates if there is uncertainty about this field",
                                    },
                                    "uncertainty_reason": {
                                        "type": "string",
                                        "description": "Short explanation if uncertainty still exists",
                                    },
                                    "uncertainty_type": {
                                        "type": "string",
                                        "description": "Type of uncertainty (e.g., multi_field_conflict, semantic_misalignment)",
                                        "enum": [
                                            "multi_field_conflict",
                                            "semantic_misalignment",
                                            "data_quality_concern",
                                            "business_rule_ambiguity",
                                            "none",
                                        ],
                                    },
                                    "assumption": {
                                        "type": "string",
                                        "description": "Any assumption made to justify selection",
                                    },
                                },
                                "required": [
                                    "field_name",
                                    "reasoning",
                                    "uncertainty_flag",
                                    "uncertainty_type",
                                    "uncertainty_reason",
                                    "assumption",
                                ],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": [
                        "table_name",
                        "required_fields",
                    ],
                    "additionalProperties": False,
                },
            },
        },
        "required": [
            "tables",
        ],
        "additionalProperties": False,
    },
}

agentic_table_selection_schema = {
    "name": "AgenticTableSelectionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "selected_tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the selected table"},
                        "fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string", "description": "Name of the field"},
                                    "reasoning": {"type": "string", "description": "Why this field is necessary"},
                                },
                                "required": ["field_name", "reasoning"],
                                "additionalProperties": False,
                            },
                        },
                        "relevance_reason": {"type": "string", "description": "Reasoning for selecting this table"},
                        "confidence_level": {
                            "type": "string",
                            "enum": ["high", "medium", "low"],
                            "description": "Confidence level in the table selection",
                        },
                    },
                    "required": ["table_name", "fields", "relevance_reason", "confidence_level"],
                    "additionalProperties": False,
                },
            }
        },
        "required": ["selected_tables"],
        "additionalProperties": False,
    },
}

block_fields_selection_schema = {
    "name": "BlockFieldsSelectionResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "table_name": {"type": "string", "description": "Name of the table being processed"},
            "required_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string", "description": "Name of the field"},
                        "reasoning": {"type": "string", "description": "Why this field is relevant to the question"},
                        "usage_type": {
                            "type": "string",
                            "enum": ["filtering", "grouping", "ordering", "target_metric"],
                            "description": "How this field will be used in the query",
                        },
                    },
                    "required": ["field_name", "reasoning", "usage_type"],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {
                "type": "boolean",
                "description": "Indicates if the required data is available in this table",
            },
        },
        "required": ["table_name", "required_fields", "is_data_available"],
        "additionalProperties": False,
    },
}

table_wise_field_filtering_schema = {
    "name": "TableWiseFieldFilteringResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "table_name": {"type": "string", "description": "Name of the table being processed"},
            "required_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string", "description": "Name of the field"},
                        "reasoning": {
                            "type": "string",
                            "description": "Why this field is necessary for answering the question",
                        },
                    },
                    "required": ["field_name", "reasoning"],
                    "additionalProperties": False,
                },
            },
            "filtered_out_fields": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field_name": {"type": "string", "description": "Name of the field that was filtered out"},
                        "reason_for_removal": {
                            "type": "string",
                            "description": "Brief explanation why this field was excluded",
                        },
                    },
                    "required": ["field_name", "reason_for_removal"],
                    "additionalProperties": False,
                },
            },
            "is_data_available": {
                "type": "boolean",
                "description": "Indicates if the required data is available in this table",
            },
        },
        "required": ["table_name", "required_fields", "filtered_out_fields", "is_data_available"],
        "additionalProperties": False,
    },
}

agentic_final_tables_fields_schema = {
    "name": "FinalTablesFieldsFilteringResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "final_SQL_Query_plan": {
                "type": "string",
                "description": "Clear plan describing how the query will answer the question",
            },
            "is_data_available": {"type": "boolean", "description": "Indicates if required data is available"},
            "no_uncertainty": {
                "type": "boolean",
                "description": "Indicates if there is full confidence in the selected tables and columns",
            },
            "reason": {"type": "string", "description": "Explanation for confidence judgment"},
            "table_join_strategy": {
                "type": "object",
                "properties": {
                    "joins_required": {
                        "type": "boolean",
                        "description": "Indicate if joins are required in the sql query logic",
                    },
                    "description": {"type": "string", "description": "How tables should be joined and on what keys"},
                    "join_uncertainty_flag": {
                        "type": "boolean",
                        "description": "Indicates if there is uncertainty in the join logic",
                    },
                    "join_uncertainty_reason": {"type": "string", "description": "Explanation of any join ambiguity"},
                },
                "required": ["joins_required", "description", "join_uncertainty_flag", "join_uncertainty_reason"],
                "additionalProperties": False,
            },
            "selected_tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the selected table"},
                        "fields": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "field_name": {"type": "string", "description": "Name of the field"},
                                    "reasoning": {"type": "string", "description": "Why this field is necessary"},
                                },
                                "required": ["field_name", "reasoning"],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": ["table_name", "fields"],
                    "additionalProperties": False,
                },
            },
            "discarded_tables": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the discarded table"},
                        "reason_for_removal": {"type": "string", "description": "Reason for discarding this table"},
                        "was_initially_selected": {
                            "type": "boolean",
                            "description": "Indicates if this table was previously selected",
                        },
                    },
                    "required": ["table_name", "reason_for_removal", "was_initially_selected"],
                    "additionalProperties": False,
                },
            },
        },
        "required": [
            "final_SQL_Query_plan",
            "is_data_available",
            "no_uncertainty",
            "reason",
            "table_join_strategy",
            "selected_tables",
            "discarded_tables",
        ],
        "additionalProperties": False,
    },
}

query_validation_response_schema = {
    "name": "QueryValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_valid": {"type": "boolean", "description": "Indicates whether the SQL query is valid or not"},
            "reason": {"type": "string", "description": "Brief explanation of why the SQL query is valid or invalid"},
            "issues": {
                "type": "array",
                "description": "List of issues found in the SQL query, if any",
                "items": {"type": "string"},
            },
            "required_tables": {
                "type": "array",
                "description": "List of required tables and their relevant fields",
                "items": {
                    "type": "object",
                    "properties": {
                        "table_name": {"type": "string", "description": "Name of the table"},
                        "fields": {
                            "type": "array",
                            "description": "List of fields required from this table",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["table_name", "fields"],
                    "additionalProperties": False,
                },
            },
            "correct_join_sequence": {
                "type": "array",
                "description": "Sequence of correct joins needed between tables",
                "items": {
                    "type": "object",
                    "properties": {
                        "from": {"type": "string", "description": "The table to join from"},
                        "to": {"type": "string", "description": "The table to join to"},
                        "on": {"type": "string", "description": "The join condition used to link the tables"},
                    },
                    "required": ["from", "to", "on"],
                    "additionalProperties": False,
                },
            },
            "corrected_sql_query_plan": {
                "type": "string",
                "description": "Brief description of the correct SQL query logic or strategy",
            },
        },
        "required": [
            "is_valid",
            "reason",
            "issues",
            "required_tables",
            "correct_join_sequence",
            "corrected_sql_query_plan",
        ],
        "additionalProperties": False,
    },
}

python_code_and_logic_schema = {
    "name": "PythonCodeAndLogicResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "code": {"type": "string"},
            "logic": {
                "type": "string",
                "description": "Explanation of the logic implemented in the Python code (not the sql logic) in non-technical terms using less than 20 words.",
            },
            "file_paths": {
                "type": "object",
                "properties": {
                    "csv": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of CSV file paths generated by the Python code",
                    },
                    "image": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of image file paths generated by the Python code",
                    },
                    "excel": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of Excel file paths generated by the Python code",
                    },
                },
                "required": ["csv", "image", "excel"],
                "additionalProperties": False,
            },
        },
        "required": ["code", "logic", "file_paths"],
        "additionalProperties": False,
    },
}

table_process_python_code_schema = {
    "name": "PythonCodeAndLogicResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "code": {"type": "string"},
            "logic": {
                "type": "string",
                "description": "Explanation of the logic implemented in the Python code (not the sql logic) in non-technical terms using less than 20 words.",
            },
            "file_paths": {
                "type": "object",
                "properties": {
                    "csv": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of CSV file paths generated by the Python code",
                    }
                },
                "required": ["csv"],
                "additionalProperties": False,
            },
        },
        "required": ["code", "logic", "file_paths"],
        "additionalProperties": False,
    },
}

# Code review schema
code_review_schema = {
    "name": "CodeReviewResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_revision_required": {"type": "boolean", "description": "Whether the code needs revision"},
            "review_feedback": {
                "type": "string",
                "description": "Feedback on the code, including issues found and suggestions for improvement",
            },
            "issues": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "issue_type": {
                            "type": "string",
                            "description": "Type of issue (e.g., 'error', 'warning', 'style', 'performance')",
                        },
                        "description": {"type": "string", "description": "Description of the issue"},
                        "line_number": {
                            "type": ["integer", "null"],
                            "description": "Line number where the issue occurs (if applicable)",
                        },
                        "severity": {
                            "type": "string",
                            "enum": ["critical", "high", "medium", "low"],
                            "description": "Severity of the issue",
                        },
                    },
                    "required": ["issue_type", "description", "line_number", "severity"],
                    "additionalProperties": False,
                },
                "description": "List of issues found in the code",
            },
        },
        "required": ["is_revision_required", "review_feedback", "issues"],
        "additionalProperties": False,
    },
}

insight_board_main_graph_render_schema = {
    "name": "DataValidationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "is_rendered": {"type": "boolean"},
            "reason": {
                "type": "string",
                "description": "Reason for not rendering the graph and suggestion to make it renderable if is_rendered is false in less than 30 words",
            },
            "kpi_name": {"type": "string", "description": "Title of the KPI evaluated in less than 10 words"},
            "visualization": {
                "type": "object",
                "properties": {
                    "title": {"type": ["string", "null"]},
                    "chartType": {"type": ["string", "null"]},
                    "labels": {"type": "array", "items": {"type": "string"}},
                    "xAxisTitle": {"type": ["string", "null"]},
                    "yAxisTitle": {"type": ["string", "null"]},
                    "datasets": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "label": {"type": ["string", "null"]},
                                "data": {"type": "array", "items": {"type": "number"}},
                                "backgroundColor": {
                                    "anyOf": [
                                        {"type": "string"},
                                        {"type": "array", "items": {"type": "string"}},
                                    ],
                                },
                                "fill": {"type": ["boolean", "null"]},
                            },
                            "required": ["label", "data", "backgroundColor", "fill"],
                            "additionalProperties": False,
                        },
                    },
                },
                "required": [
                    "title",
                    "chartType",
                    "labels",
                    "xAxisTitle",
                    "yAxisTitle",
                    "datasets",
                ],
                "additionalProperties": False,
            },
            "visualization_summary": {
                "type": "object",
                "properties": {
                    "current_value": {"type": ["number", "null"]},
                    "previous_value": {"type": ["number", "null"]},
                    "currency_sign": {"type": ["string", "null"]},
                    "statement": {
                        "type": ["string", "null"],
                        "description": "Summarized interpretation of the trend in less than 15 words.",
                    },
                },
                "required": ["current_value", "previous_value", "currency_sign", "statement"],
                "additionalProperties": False,
            },
        },
        "required": ["is_rendered", "reason", "kpi_name", "visualization", "visualization_summary"],
        "additionalProperties": False,
    },
}

insight_board_further_analysis_render_schema = {
    "name": "InsightBoardFurtherAnalysisRenderResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "key_insights": {"type": "array", "items": {"type": "string"}},
            "observation_list": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "observation_title": {"type": "string"},
                        "observation_description": {"type": ["string", "null"]},
                        "observation_items": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "type": {"type": "string", "enum": ["table", "image"]},
                                    "title": {"type": "string"},
                                    "file_name": {"type": ["string", "null"]},
                                },
                                "required": ["type", "title", "file_name"],
                                "additionalProperties": False,
                            },
                        },
                    },
                    "required": ["observation_title", "observation_description", "observation_items"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["key_insights", "observation_list"],
        "additionalProperties": False,
    },
}

insight_board_identify_new_key_findings_schema = {
    "name": "InsightBoardIdentifyNewKeyFindingsResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "new_key_findings": {
                "type": "array",
                "items": {"type": "string"},
                "description": "List of significant new key findings",
            },
        },
        "required": ["new_key_findings"],
        "additionalProperties": False,
    },
}

# Question combine response schema
question_combine_response_schema = {
    "name": "QuestionCombineResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "combined_question": {"type": "string"},
            "is_related": {"type": "boolean"},
        },
        "required": ["combined_question", "is_related"],
        "additionalProperties": False,
    },
}

# Query routing schema
query_route_response_schema = {
    "name": "IntentClassificationResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "route_option": {
                "type": "string",
                "enum": ["DATA", "METADATA", "PYTHON", "NONE"],
            },
            "data_sources": {"type": "array", "items": {"type": "string"}},
            "reasoning": {"type": "string"},
            "response": {"type": "string"},
            "title": {"type": "string", "description": "Title for the user query (Max: 10 words)"},
        },
        "required": ["route_option", "data_sources", "reasoning", "response", "title"],
        "additionalProperties": False,
    },
}

# Answer formatting schema
formatted_answer_schema = {
    "name": "FormattedAnswerResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "final_answer": {"type": "string"},
        },
        "required": ["final_answer"],
        "additionalProperties": False,
    },
}

# Content archiving
content_summarize_schema = {
    "name": "ContentSummarizeResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "reference_id": {
                "type": "string",
                "description": "A single word, Unique and meaningful identifier for the archived content (Max. 15 characters)",
            },
            "summary": {
                "type": "string",
                "description": "Summary of the archived content mentioning what it contains",
            },
        },
        "required": ["reference_id", "summary"],
        "additionalProperties": False,
    },
}

# Knowledge Tree Updating
knowledge_tree_update_schema = {
    "name": "KnowledgeTreeUpdateResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "new_patterns": {
                "type": "array",
                "description": "New entities and analysis patterns to be added to the knowledge tree.",
                "items": {
                    "type": "object",
                    "properties": {
                        "entity": {
                            "type": "string",
                            "description": "New or existing entity under which the analysis pattern is added.",
                        },
                        "pattern": {
                            "type": "string",
                            "description": "Name of the analysis pattern to be added under the entity.",
                        },
                        "pattern_key": {
                            "type": "string",
                            "description": "Unique key for the analysis pattern.",
                        },
                    },
                    "required": ["entity", "pattern", "pattern_key"],
                    "additionalProperties": False,
                },
            },
            "block_assignments": {
                "type": "array",
                "description": "Knowledge blocks to be assigned to the new or existing nodes.",
                "items": {
                    "type": "object",
                    "properties": {
                        "block_id": {"type": "string", "description": "ID of the knowledge block to be assigned."},
                        "pattern_keys": {
                            "type": "array",
                            "description": "List of pattern keys to which the block is assigned.",
                            "items": {"type": "string"},
                        },
                    },
                    "required": ["block_id", "pattern_keys"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["new_patterns", "block_assignments"],
        "additionalProperties": False,
    },
}

cognitive_match_schema = {
    "name": "CognitiveMatchResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "text_indexes": {
                "type": "array",
                "items": {"type": "integer"},
            },
            "reasoning": {"type": "string", "description": "Reasoning behind the match in less than 50 words."},
        },
        "required": ["text_indexes", "reasoning"],
        "additionalProperties": False,
    },
}

visual_data_extractor_schema = {
    "name": "VisualDataExtractorResponse",
    "strict": True,
    "schema": {
        "type": "object",
        "properties": {
            "csv_files": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "file_name": {"type": "string"},
                        "file_data": {
                            "type": "string",
                            "description": "List of JSON objects representing the rows of the CSV file.",
                        },
                    },
                    "required": ["file_name", "file_data"],
                    "additionalProperties": False,
                },
            },
        },
        "required": ["csv_files"],
        "additionalProperties": False,
    },
}
