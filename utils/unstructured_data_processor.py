"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class UnstructuredDataProcessor
* @description Handles the unstructured data cache querying and updating
* <AUTHOR>
"""

from logging import Logger
import os
import pathlib
from utils.misc_utils import batch_generator
from utils.llm_utils import (
    check_unrelevent,
    get_dict_from_json_or_python,
    get_python_actions,
    truncate_data,
    labels_merge_schema,
    reusable_labels_output_schema,
    additional_labels_output_schema,
    cognitive_match_schema,
)
from copy import deepcopy
from utils.logger import get_debug_logger
from services.global_memory_service import GlobalMemoryService
from services.unstructured_data_cache_service import UnstructuredDataCacheService
from services.conversation_service import ConversationService
from pandas import DataFrame
from services.conversation_service import ConversationService
from services.llm_client import LLMClient

SEMANTIC_SEARCH_TEXT_BATCH_SIZE = 20


class UnstructuredDataProcessor:
    def __init__(self, _llm_client):
        self.llm_name_unstructured_processing = os.getenv("MODEL_UNSTRUCTURED_PROCESSING")
        if not self.llm_name_unstructured_processing:
            raise ValueError("MODEL_UNSTRUCTURED_PROCESSING env variable not present")

        self.llm_name_semantic_search = os.getenv("MODEL_UNSTRUCTURED_SEMANTIC_SEARCH", "gpt-4.1")

        self.llm_name_label_identification = os.getenv("MODEL_UNSTRUCTURED_LABEL_IDENTIFICATION")
        if not self.llm_name_label_identification:
            raise ValueError("MODEL_UNSTRUCTURED_LABEL_IDENTIFICATION env variable not present")
        self.unstructured_cache_handler = UnstructuredDataCacheService(
            llm_name_label_logic=self.llm_name_label_identification
        )
        self.conversation_service = ConversationService()
        self.global_memory = GlobalMemoryService()
        self.llm_client: LLMClient = _llm_client
        self.logger = get_debug_logger(
            "unstructured_data_handler",
            pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/unstructured_data.log"),
        )
        with open("./instructions/unstructured_data/semantic_text_matching.txt", "r", encoding="utf-8") as file:
            self.semantic_search_instructions = file.read()

    def has_duplicate_ids(self, dict_list):
        seen_ids = set()
        for d in dict_list:
            uid = d.get("unique_id")
            if uid in seen_ids:
                return True  # Duplicate found
            seen_ids.add(uid)
        return False

    def query_unstructured_data(
        self,
        chat_id,
        chat_log,
        unstructured_data_list,
        data_source_name,
        table_name,
        column_name,
        labelling_instruction,
        output_labels=[],
        primary_label=None,
    ):
        """
        Description:
            Processes and classifies unstructured data such as comments, notes, reviews, and feedback found in database tables using a Large Language Model (LLM). It classifies the data based on user-defined criteria, making it usable for generating answers or insights.

        Parameters:
            chat_id (str): Unique identifier for the function call session.
            unstructured_data_list (list of dict): List of dictionaries containing unstructured data fields and associated
                key information.
            data_source_name (str): Name of the data source according to the data dictionary.
            table_name (str): Name of the table containing the unstructured data field.
            column_name (str): Name of the column containing the unstructured data.
            labelling_instruction (str): Instruction for the classification task based on user requirements.
            output_labels (list of str, optional): List of predefined classification labels to be in the output.
            primary_label (str, optional): Primary label for secondary level classification, if applicable.

        Returns:
            dict: A dictionary with classification labels as keys and lists of dictionaries as values, representing
            classified data items. The dictionary may include an "Unclassified" category for data that cannot be
            classified.
            int: Returns -1 on failure to process the data.

        Example Input:
            [
                {
                    "purchase_id": "A100001",
                    "customer_id": "C1001",
                    "COMMENT": "The item arrived broken and damaged."
                },
                ...
            ]

        Example Output:
            {
                "is_success": True,
                "categorization":{
                        "Damaged Item": [
                            {
                                "purchase_id": "A100001",
                                "customer_id": "C1001"
                            }
                        ],
                        ...
                },
                "total_record_count": 3
            }
        """

        all_labels_topic_wise = {}
        label_master = self.unstructured_cache_handler.query_labelling_master(
            data_source_name, table_name, column_name, unstructured_data_list
        )
        if not label_master:
            return {
                "is_success": False,
                "record_count": len(unstructured_data_list),
                "categorization": {},
                "error": "No label master data in cache",
            }

        all_labels_topic_wise = label_master["topic_labels_dict"]
        relevant_labels_dict = {}
        field_id = label_master["field_id"] if "field_id" in label_master else -1
        all_labels_list = [label for labels in all_labels_topic_wise.values() for label in labels]

        # Try re-using the output label mapping if already determined in the previous step in this conversation if it matches the current labelling instruction
        # This is done to avoid doing a different label mapping or grouping in case of followup  question.

        # Load the previous output label mapping from the database
        prev_label_mapping = self.conversation_service.get_unstructured_data_label_mapping(chat_id, True, chat_log)

        if prev_label_mapping and prev_label_mapping["labelMapping"]:
            prev_mapped_labels = prev_label_mapping["labelMapping"]
            chat_log.debug(
                f"metalake | process_unstructured_data | previous label mapping:\n {str(prev_mapped_labels)}"
            )
            prev_labelling_task = prev_label_mapping["labellingTask"]
            relevant_labels_dict = self.unstructured_cache_handler.llm_reuse_label_mapping(
                self.llm_client,
                labelling_instruction,
                prev_labelling_task,
                prev_mapped_labels,
                all_labels_topic_wise,
                output_labels,
                chat_log,
            )

        if relevant_labels_dict:
            output_labels = list(relevant_labels_dict.keys())
            chat_log.debug(f"metalake | process_unstructured_data | label reuse list:\n {output_labels}")
        else:
            # No re-use of label mapping - determine the relevant labels
            # Filter topics based on question
            filtered_topics_dict = self.unstructured_cache_handler.llm_filter_topics(
                self.llm_client, labelling_instruction, output_labels, all_labels_topic_wise, chat_log
            )
            # Output Format:
            # {
            #    "filtered_topics": ["<List of topics>"],
            #    "is_full_match": true/false,
            #    "relevant_label_mapping": <same as relevant_labels_dict>
            #    "output_labels": ["<List of output labels if not provided and only one topic selected>"]
            # }
            if filtered_topics_dict and filtered_topics_dict["filtered_topics"]:
                relevant_labels_dict = filtered_topics_dict["relevant_label_mapping"]
                # Now we can limit the label matching to the filtered topics
                if filtered_topics_dict["is_full_match"]:
                    if not output_labels:
                        output_labels = filtered_topics_dict["output_labels"]
                    # No need to further filtering of granular labels
                else:
                    filtered_topic_wise_labels = {}
                    for topic_name in filtered_topics_dict["filtered_topics"]:
                        filtered_topic_wise_labels[topic_name] = all_labels_topic_wise[topic_name]
                    # Do filtering of granular labels under filtered topics
                    relevant_labels_dict = self.unstructured_cache_handler.llm_filter_labels(
                        self.llm_client, labelling_instruction, output_labels, filtered_topic_wise_labels, chat_log
                    )
                    # If output labels are not given, then topic name is the output label
                    if not output_labels:
                        output_labels = filtered_topics_dict["filtered_topics"]
            else:
                # Need to do granular level filtering of all topics
                if not output_labels:
                    # Determine the output labels if not provided
                    output_labels, relevant_labels_dict = self.unstructured_cache_handler.llm_generate_output_labels(
                        self.llm_client,
                        chat_id,
                        all_labels_topic_wise,
                        labelling_instruction,
                        chat_log,
                    )
                    if output_labels == -1:
                        chat_log.warning("metalake | process_unstructured_data | llm_generate_output_labels failed")
                        return {
                            "is_success": False,
                            "error": "metalake | process_unstructured_data | llm_generate_output_labels failed",
                            "record_count": len(unstructured_data_list),
                            "categorization": {},
                        }

                else:
                    # Identify the related labels for the given labelling task or question
                    relevant_labels_dict = self.unstructured_cache_handler.llm_get_relevant_labels(
                        self.llm_client,
                        labelling_instruction,
                        output_labels,
                        all_labels_list,
                        chat_log,
                    )

            self.conversation_service.set_unstructured_data_label_mapping(
                chat_id, labelling_instruction, relevant_labels_dict
            )

        if not relevant_labels_dict:
            chat_log.warning("metalake | process_unstructured_data | llm_get_relevant_labels failed")
            return {
                "is_success": False,
                "error": "metalake | process_unstructured_data | llm_get_relevant_labels failed",
                "record_count": len(unstructured_data_list),
                "categorization": {},
            }

        chat_log.debug(f"metalake | process_unstructured_data | relevant labels matched:\n {relevant_labels_dict}")
        all_relevant_label_list = [
            label for output_label, label_list in relevant_labels_dict.items() for label in label_list
        ]
        chat_log.debug(f"metalake | process_unstructured_data | all_relevant_label_list: {all_relevant_label_list}")

        # Query the cache and find the both the categories or labels already exists and
        # the data items already processed and items to process with AI
        cache_result = self.unstructured_cache_handler.cache_lookup(
            unstructured_data_list,
            field_id,
            column_name,
            all_relevant_label_list,
        )
        chat_log.debug(
            f"metalake | process_unstructured_data | cache_result: Cache hit count = {len(cache_result['processed_data_from_cache'])}, Cache missed count = {len(cache_result['cache_missed_data_list'])}"
        )
        labelled_data_output = cache_result["processed_data_from_cache"]

        # Organize the records from cache according to the output labels
        output_dict = self.unstructured_cache_handler.merge_cache_results(
            labelled_data_output, relevant_labels_dict, len(output_labels) > 0
        )

        chat_log.debug(f"metalake | process_unstructured_data | labelled output: {output_dict}")
        chat_log.info(
            f"metalake | process_unstructured_data | counts per label: {', '.join([f'{key}: {len(value)}' for key, value in output_dict.items()])}"
        )
        return {"is_success": True, "total_record_count": len(unstructured_data_list), "categorization": output_dict}

    def generate_unstructured_data_cache(
        self,
        chat_id,
        unstructured_data_list,
        data_source_name,
        table_name,
        column_name,
        labelling_instruction,
        output_labels=[],
        primary_label=None,
    ):
        """
        Description:
            Processes and classifies unstructured data such as comments, notes, reviews, and feedback found in database tables using a Large Language Model (LLM). It classifies the data based on user-defined criteria, making it usable for generating answers or insights.
            Then updates the cache with the categorized labelling.
        Parameters:
            chat_id (str): Unique identifier for the function call session.
            unstructured_data_list (list of dict): List of dictionaries containing unstructured data fields and associated
                key information.
            data_source_name (str): Name of the data source according to the data dictionary.
            table_name (str): Name of the table containing the unstructured data field.
            column_name (str): Name of the column containing the unstructured data.
            labelling_instruction (str): Instruction for the classification task based on user requirements.
            output_labels (list of str, optional): List of predefined classification labels to be in the output.
            primary_label (str, optional): Primary label for secondary level classification, if applicable.

        Returns:
            dict: A dictionary with classification labels as keys and lists of dictionaries as values, representing
            classified data items. The dictionary may include an "Unclassified" category for data that cannot be
            classified.
            int: Returns -1 on failure to process the data.

        Example Input:
            [
                {
                    "purchase_id": "A100001",
                    "customer_id": "C1001",
                    "COMMENT": "The item arrived broken and damaged."
                },
                ...
            ]

        Example Output:
            {
                "Damaged Item": [
                    {
                        "purchase_id": "A100001",
                        "customer_id": "C1001"
                    }
                ],
                ...
            }
        """

        BATCH_SIZE = 10
        REVIEW_BATCH_COUNT = 5  # Review the labels for duplicates and meaningless ones once every 10 batches
        review_count = 0

        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")

        chat_log.debug(
            f"metalake | fill_unstructured_data_cache | table = {table_name}, column = {column_name}: processing unstructured data, batch size = {len(unstructured_data_list)}"
        )

        # Calculate unique hash value for each unstructured record and attache
        self.unstructured_cache_handler.set_unique_hash(unstructured_data_list, column_name)

        ## First clear the cache for unstructured field
        # self.unstructured_cache_handler.reset_cache(data_source_name, table_name, column_name)

        # Load current label set from cache topic-wise
        label_master = self.unstructured_cache_handler.query_labelling_master(
            data_source_name,
            table_name,
            column_name,
        )

        topic_labels_dict = {}
        if label_master and "topic_labels_dict" in label_master:
            topic_labels_dict = label_master["topic_labels_dict"]

        # Break data in to processing batches and then label reviewing batches (eg: Every 5 processing batch reviewed)
        batch_list = batch_generator(unstructured_data_list, BATCH_SIZE)
        # Break batch_list to review sets of size REVIEW_BATCH_COUNT
        review_batch_list = [
            batch_list[i : i + REVIEW_BATCH_COUNT] for i in range(0, len(batch_list), REVIEW_BATCH_COUNT)
        ]

        reviewed_label_list = []
        labelled_count = 0

        for review_batch in review_batch_list:
            review_count = review_count + 1
            _intermediate_results = []
            intermediate_labels_generated = []
            for batch in review_batch:

                # Identify the matching labels for the batch of records -

                # re-use existing labels
                reusable_topic_labels_dict = self.llm_get_unstructured_data_labels_existing(
                    batch,
                    data_source_name,
                    table_name,
                    column_name,
                    labelling_instruction,
                    topic_labels_dict,
                    chat_id,
                )

                # create new if necessary
                additional_labels_list = self.llm_get_unstructured_data_labels_create(
                    batch,
                    data_source_name,
                    table_name,
                    column_name,
                    labelling_instruction,
                    topic_labels_dict,
                    reusable_topic_labels_dict,
                    chat_id,
                )

                if reusable_topic_labels_dict == -1 and additional_labels_list == -1:
                    chat_log.error(
                        f"metalake | fill_unstructured_data_cache | llm_get_unstructured_data_labels | failed to get labels"
                    )
                    return []

                labels_list = [label for topic in reusable_topic_labels_dict.values() for label in topic]
                labels_list.extend(additional_labels_list)

                labels_list = list(set(labels_list))

                # Put the comments into given labels
                batch_result = self.process_unstructured_data_batch(
                    batch,
                    labelling_instruction,
                    column_name,
                    labels_list,
                    True,
                    chat_id,
                )
                if batch_result == -1:
                    chat_log.error(
                        f"metalake | fill_unstructured_data_cache | process_unstructured_data_batch | failed to process batch"
                    )
                    return []

                _intermediate_results.append(batch_result)
                labelled_count += len(batch)
                temp_label_list = list({k for d in _intermediate_results for k in d.keys()})
                temp_label_list.extend(reviewed_label_list)
                intermediate_labels_generated = list(set(temp_label_list))
                chat_log.debug(f"Finished labelling {labelled_count}")
                chat_log.debug(f"Current label set: {intermediate_labels_generated}")

            # Review for duplicate labels and merge them
            # all_labels_data_list =  _intermediate_results
            duplicates_list = self.llm_review_unstructured_data_labels(
                _intermediate_results, topic_labels_dict, chat_id, label_master["field_id"]
            )
            if duplicates_list == -1:
                chat_log.error(
                    f"metalake | fill_unstructured_data_cache | llm_review_unstructured_data_labels | failed to review labels"
                )
                return []

            reviewed_intermediate_result_dict_list = []

            chat_log.debug(f"Duplicate analysis output: \n{duplicates_list}")

            # Now find all duplicate in labels from intermediate results and merge them
            for result in _intermediate_results:
                new_result_dict = {}
                for key, value in result.items():
                    is_key_merged = False
                    for duplicate in duplicates_list:
                        duplicate_in_labels = duplicate["duplicate_in"]
                        if key in duplicate_in_labels:
                            merged_label = duplicate["merged_label"]
                            if merged_label in new_result_dict:
                                # new_result_dict[merged_label].extend(value)
                                for val in value:
                                    if not any(
                                        d.get("unique_id") == val.get("unique_id")
                                        for d in new_result_dict[merged_label]
                                    ):
                                        new_result_dict[merged_label].append(val)
                            else:
                                new_result_dict[merged_label] = deepcopy(value)
                            is_key_merged = True
                            break

                    if not is_key_merged:
                        if key in new_result_dict:
                            new_result_dict[key].extend(deepcopy(value))
                        else:
                            new_result_dict[key] = deepcopy(value)
                reviewed_intermediate_result_dict_list.append(new_result_dict)

            # Update the topic_labels_dict based on merged labels
            for topic, labels in topic_labels_dict.items():
                updated_labels = []
                for label in labels:
                    is_merged = False
                    for duplicate in duplicates_list:
                        duplicate_in_labels = duplicate["duplicate_in"]
                        merged_label = duplicate["merged_label"]
                        if label in duplicate_in_labels:
                            updated_labels.append(merged_label)
                            is_merged = True
                            break
                    if not is_merged:
                        updated_labels.append(label)
                topic_labels_dict[topic] = list(set(updated_labels))

            # Update the all labels list after merge operations
            reviewed_label_list = list(
                set(list({k for d in reviewed_intermediate_result_dict_list for k in d.keys()}))
            )

            chat_log.info(f"review_count: {review_count}")
            chat_log.info(
                f"review_count: {review_count} | reviewed_intermediate_result_dict_list: {reviewed_intermediate_result_dict_list}"
            )
            chat_log.info(f"review_count: {review_count} | _intermediate_results: {_intermediate_results}")
            chat_log.debug(f"review_count: {review_count} | Duplicate analysis output: \n{duplicates_list}")
            chat_log.debug(
                f"review_count: {review_count} | Label set after review:\n {reviewed_label_list} \nOrganized into topics:\n {topic_labels_dict}"
            )

            # Save the labels identified so far into cache
            # Merge intermediate results
            merged_results = {}
            for result in reviewed_intermediate_result_dict_list:
                for key, value in result.items():
                    if key in merged_results:
                        merged_results[key].extend(deepcopy(value))
                    else:
                        merged_results[key] = deepcopy(value)
                    # duplicate validation
                    if self.has_duplicate_ids(merged_results[key]):
                        chat_log.error(
                            f"metalake | fill_unstructured_data_cache | duplicate ids found in merged results for label: {key}"
                        )

            if merged_results:
                chat_log.debug(f"metalake | fill_unstructured_data_cache | merged_results:\n {merged_results}")
                # Update the cache
                self.unstructured_cache_handler.update_cache(
                    merged_results,
                    data_source_name,
                    table_name,
                    column_name,
                    topic_labels_dict,
                    duplicates_list,
                    chat_log,
                )

        return merged_results

    def llm_get_unstructured_data_labels_existing(
        self,
        unstructured_data,
        data_source_name,
        table_name,
        column_name,
        task_instruction,
        topics_labels_dict,
        chat_id,
    ):
        """
        Description:
          Get a list of matching categories or labels for given unstructured data records

        Parameters:
        - unstructured_data (list of dictionaries): A list of dictionaries containing the unstructured data in the format:
            [{"<unstructured_column_name>": "<unstructured_data_value>", "unique_id": <hash value>}]
        - task_instruction (str): The instruction to perform the task based on user question.
        - topic_labels_dict (dict): A dictionary with labels under each topic.
        - chat_id (str): A unique identifier for the current chat session.

        Returns:
        - A dictionary with key-value pairs where key is a category and value is a list of dictionaries containing the structured data.
        - -1 if the data is unavailable or processing fails.
        """

        from services.data_dictionary_service import DataDictionaryService

        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
        chat_log.debug(f"metalake | llm_get_unstructured_data_labels_existing | selecting exiting label...")
        # Get the unstructured data in to a simple array
        unstructured_data_array = []
        for record in unstructured_data:
            unstructured_data_array.append(record[column_name])
        # Load unstructured column description from data dictionary
        data_dictionary_handler = DataDictionaryService()
        column_desc = data_dictionary_handler.get_field_description(data_source_name, table_name, column_name)

        instruction_file = "unstructured_data_label_reuse.txt"

        instructions = ""
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        flattened_label_list = [label for topic in topics_labels_dict.values() for label in topic]
        batch_size = 250
        label_list_batches = batch_generator(flattened_label_list, batch_size)

        all_reusable_labels = []

        for label_list_batch in label_list_batches:

            llm_prompt = []
            llm_prompt.append({"role": "system", "content": instructions})
            llm_prompt.append({"role": "user", "content": "Labelling task/Purpose: " + str(task_instruction)})
            # llm_prompt.append({"role": "user", "content": "Topics covered by the data: " + str(topics_list)})
            llm_prompt.append(
                {"role": "user", "content": "Unstructured data field description/overview: " + str(column_desc)}
            )
            llm_prompt.append({"role": "user", "content": "Existing Labels: " + str(label_list_batch)})
            llm_prompt.append(
                {"role": "user", "content": "Unstructured data records:\n" + str(unstructured_data_array)}
            )

            llm_output = None
            try:
                chat_log.debug(f"metalake | llm_get_unstructured_data_labels_existing | llm_prompt = {llm_prompt}")
                response = self.llm_client.get_llm_response(
                    message_list=llm_prompt,
                    model=self.llm_name_label_identification,
                    seed=1234,
                    token_limit=4096,
                    response_format=reusable_labels_output_schema,
                )
                llm_output = response.choices[0].message.content
                chat_log.debug(f"metalake | llm_get_unstructured_data_labels_existing | llm_output = {llm_output}")
                # count tokens
                # self.update_token_count(response, chat_id)
            except Exception as e:
                chat_log.error(f"metalake | llm_get_unstructured_data_labels_existing | error = {e}")

            if llm_output is None:
                chat_log.debug("metalake | llm_get_unstructured_data_labels_existing | llm_output is None")
                return -1
            elif check_unrelevent(llm_output):
                chat_log.debug("metalake | llm_get_unstructured_data_labels_existing | llm_output is irrelevant")
                return -1
            else:
                labels_output_dict = get_dict_from_json_or_python(llm_output)
                chat_log.debug(f"metalake | llm_get_unstructured_data_labels_existing | result = {labels_output_dict}")
                """
                Dictionary format:
                {
                    "labels_reused": ["Late Delivery"],
                }
                """
                # Update the topics_labels_dict according to the output
                _reusable_labels_list = labels_output_dict["labels_reused"]
                if _reusable_labels_list:
                    all_reusable_labels.extend(_reusable_labels_list)

        # Create new dictionary with only selected labels
        reusable_topic_labels = {}
        for topic, labels in topics_labels_dict.items():
            # Filter the labels
            filtered = [label for label in labels if label in all_reusable_labels]
            if filtered:  # Only add topics that have at least one matching label
                reusable_topic_labels[topic] = filtered

        return reusable_topic_labels

    def llm_get_unstructured_data_labels_create(
        self,
        unstructured_data,
        data_source_name,
        table_name,
        column_name,
        task_instruction,
        topics_labels_dict,
        reusable_topic_labels_dict,
        chat_id,
    ):
        """
        Description:
          Get a list of matching categories or labels for given unstructured data records

        Parameters:
        - unstructured_data (list of dictionaries): A list of dictionaries containing the unstructured data in the format:
            [{"<unstructured_column_name>": "<unstructured_data_value>", "unique_id": <hash value>}]
        - task_instruction (str): The instruction to perform the task based on user question.
        - topic_labels_dict (dict): A dictionary with labels under each topic.
        - chat_id (str): A unique identifier for the current chat session.

        Returns:
        - A dictionary with key-value pairs where key is a category and value is a list of dictionaries containing the structured data.
        - -1 if the data is unavailable or processing fails.
        """
        # Hard coded topics
        ALL_TOPICS = [
            "Security",
            "Flight",
            "Facilities and Amenities",
            "Entertainment",
            "Food",
            "Atmosphere",
            "Transport",
            "Check-in",
            "Baggage",
            "Customs",
            "General",
        ]
        from services.data_dictionary_service import DataDictionaryService

        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
        chat_log.debug(
            f"metalake | llm_get_unstructured_data_labels_create | processing unstructured data, batch size = {len(unstructured_data)}"
        )
        # Get the unstructured data in to a simple array
        unstructured_data_array = []
        for record in unstructured_data:
            unstructured_data_array.append(record[column_name])
        # Load unstructured column description from data dictionary
        data_dictionary_handler = DataDictionaryService()
        column_desc = data_dictionary_handler.get_field_description(data_source_name, table_name, column_name)

        instruction_file = "unstructured_data_label_additional.txt"

        instructions = ""
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append({"role": "user", "content": "Labelling task: " + str(task_instruction)})
        # llm_prompt.append({"role": "user", "content": "Topics covered by the data: " + str(topics_list)})
        llm_prompt.append({"role": "user", "content": "Unstructured data field description: " + str(column_desc)})
        llm_prompt.append(
            {
                "role": "user",
                "content": "Existing available categories under each topic: " + str(reusable_topic_labels_dict),
            }
        )
        llm_prompt.append({"role": "user", "content": "All available topics: " + str(ALL_TOPICS)})
        llm_prompt.append({"role": "user", "content": "Unstructured data input:\n" + str(unstructured_data_array)})

        llm_output = None
        try:
            chat_log.debug(f"metalake | llm_get_unstructured_data_labels_create | llm_prompt = {llm_prompt}")
            response = self.llm_client.get_llm_response(
                message_list=llm_prompt,
                model=self.llm_name_label_identification,
                seed=1234,
                token_limit=4096,
                response_format=additional_labels_output_schema,
            )
            llm_output = response.choices[0].message.content
            chat_log.debug(f"metalake | llm_get_unstructured_data_labels_create | llm_output = {llm_output}")
            # count tokens
            # self.update_token_count(response, chat_id)
        except Exception as e:
            chat_log.error(f"metalake | llm_get_unstructured_data_labels_create | error = {e}")

        if llm_output is None:
            chat_log.debug("metalake | llm_get_unstructured_data_labels_create | llm_output is None")
            return -1
        elif check_unrelevent(llm_output):
            chat_log.debug("metalake | llm_get_unstructured_data_labels_create | llm_output is irrelevant")
            return -1
        else:
            labels_output_dict = get_dict_from_json_or_python(llm_output)
            chat_log.debug(f"metalake | llm_get_unstructured_data_labels_create | result = {labels_output_dict}")
            """
            Dictionary format:
            {
                "new_labels": [
                    {
                        "topic": "Delivery",
                        "labels": ["Delivered on time"]
                    },
                    {
                        "topic": "Customer Service",
                        "labels": ["Customer Care Not Responsive"]
                    },
                    {
                        "topic": "Quality",
                        "labels":["Damaged Item"]
                    }
                ]
            }
            """
            # Update the topics_labels_dict according to the output
            new_labels_list = []
            if "new_labels" in labels_output_dict:
                for new_label in labels_output_dict["new_labels"]:
                    new_labels_list.extend(new_label["labels"])
                    if new_label["topic"] in topics_labels_dict:
                        topics_labels_dict[new_label["topic"]].extend(new_label["labels"])
                    else:
                        topics_labels_dict[new_label["topic"]] = new_label["labels"]
                    # Make sure no duplicate labels for topic (in case was put by LLM)
                    topics_labels_dict[new_label["topic"]] = list(set(topics_labels_dict[new_label["topic"]]))

            return new_labels_list

    def llm_review_unstructured_data_labels(
        self,
        intermediate_results,
        topic_labels_dict,
        chat_id,
        field_id,
    ):
        """
        Description:
          Identify duplicate labels and suggest how to merge those

        Parameters:
        - all_labels_list (list of dictionaries): A list of dictionaries containing the  unstructured data categorization for each label.
            format: [{"label1": [data_list1], "label2": [data_list2]}, {"label2": [data_list3], "label3": [data_list4]}]

        - topic_labels_dict (dict): A dictionary containing the labels for each topic.
            format: {"topic1": ["label1", "label2"], "topic2": ["label3", "label4"]}
        - chat_id (str): The chat id

        Returns:
        - List of labels to merge
        """

        label_counts_dict = self.unstructured_cache_handler.cache_count_label_dict(field_id)

        for label_data in intermediate_results:
            for label, data_list in label_data.items():
                if label not in label_counts_dict:
                    label_counts_dict[label] = len(data_list)
                else:
                    label_counts_dict[label] += len(data_list)
        # Get the label counts organized under topics using topics_labels_dict - in the format {"<topic>": [{"label": "<label1>", "count": <count1>}, {"label": "<label2>", "count": <count2>}]}
        topic_label_counts_dict = {}
        for topic, labels in topic_labels_dict.items():
            topic_label_counts_dict[topic] = []
            for label in labels:
                if label in label_counts_dict:
                    topic_label_counts_dict[topic].append({"label": label, "count": label_counts_dict[label]})
            # Sort the labels under this topic according to count
            topic_label_counts_dict[topic] = sorted(
                topic_label_counts_dict[topic], key=lambda x: x["count"], reverse=True
            )

        # Do the review separately for each topic
        all_duplicates_list = []
        for topic, labels in topic_label_counts_dict.items():
            # Skip the review if there is only one label
            if len(labels) == 1:
                continue
            chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
            chat_log.debug(f"metalake | llm_review_unstructured_data_labels | Reviewing labels = {labels}")

            instruction_file = "unstructured_data_label_review.txt"

            instructions = ""
            with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
                instructions = file.read()

            llm_prompt = []
            llm_prompt.append({"role": "system", "content": instructions})
            # llm_prompt.append({"role": "user", "content": "Labelling task: " + str(task_instruction)})
            # llm_prompt.append({"role": "user", "content": "Input labels:\n" + str(all_labels_list)})
            llm_prompt.append(
                {"role": "user", "content": "Strictly follow the given instructions and generate the output."}
            )
            llm_prompt.append({"role": "assistant", "content": "Input labels with frequency:\n" + str(labels)})

            llm_output = None
            try:
                chat_log.debug(f"metalake | llm_review_unstructured_data_labels | llm_prompt = {llm_prompt}")
                response = self.llm_client.get_llm_response(
                    message_list=llm_prompt,
                    model=self.llm_name_label_identification,
                    seed=1234,
                    token_limit=4096,
                    response_format=labels_merge_schema,
                )
                llm_output = response.choices[0].message.content
                chat_log.debug(f"metalake | llm_review_unstructured_data_labels | llm_output = {llm_output}")
                # count tokens
                # self.update_token_count(response, chat_id)
            except Exception as e:
                chat_log.error(f"metalake | llm_review_unstructured_data_labels | error = {e}")

            if llm_output is None:
                chat_log.debug("metalake | llm_review_unstructured_data_labels | llm_output is None")
                return -1
            elif check_unrelevent(llm_output):
                chat_log.debug("metalake | llm_review_unstructured_data_labels | llm_output is irrelevant")
                return -1
            else:
                duplicates_list_dict = get_dict_from_json_or_python(llm_output)
                chat_log.debug(f"metalake | llm_get_unstructured_data_labels | result = {duplicates_list_dict}")
            if "duplicate_labels" in duplicates_list_dict and duplicates_list_dict["duplicate_labels"]:
                all_duplicates_list.extend(duplicates_list_dict["duplicate_labels"])

        return all_duplicates_list

    def process_unstructured_data_batch(
        self,
        unstructured_data,
        task_instruction,
        unstructured_column,
        existing_categories,
        is_categories_fixed,
        chat_id,
    ):
        """
        Description:
          Process a batch of unstructured data using an LLM - Convert the unstructured data list to key value dictionary where key is incremental index (starting from 1) and value is the "unstructured_column" field

        Parameters:
        - unstructured_data (list of dictionaries): A list of dictionaries containing the unstructured data.
        - task_instruction (str): The instruction to perform the task based on user question.
        - unstructured_column (str): The name of the column in the unstructured data that should be processed.
        - existing_categories (list of str): The existing categories for consistent categorization.
        - is_categories_fixed (bool): A boolean indicating whether the categories are fixed or not.
        - chat_id (str): A unique identifier for the current chat session.

        Returns:
        - A dictionary with key-value pairs where key is a category and value is a list of dictionaries containing the structured data.
        - -1 if the data is unavailable or processing fails.
        """
        unstructured_input_dict = {}
        index = 0
        for item in unstructured_data:
            # Avoid empty data
            if unstructured_column not in item or not item[unstructured_column]:
                continue
            index += 1
            unstructured_input_dict[index] = item[unstructured_column]

        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
        chat_log.debug(
            f"metalake | process_unstructured_data_batch | processing unstructured data, batch = {unstructured_input_dict}"
        )
        chat_log.debug(
            f"metalake | process_unstructured_data_batch | processing unstructured data, batch size = {len(unstructured_data)}"
        )

        if is_categories_fixed:
            instruction_file = "unstructured_data_process_fixed_classification.txt"
        else:
            instruction_file = "unstructured_data_process_dynamic_classification.txt"

        instructions = ""
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append(
            {
                "role": "user",
                "content": (
                    "Available categories/labels:\n" + str(existing_categories)
                    if existing_categories
                    else "None initially provided."
                ),
            }
        )
        llm_prompt.append({"role": "user", "content": "Unstructured data:\n" + str(unstructured_input_dict)})

        llm_output = None
        try:
            chat_log.debug(f"metalake | process_unstructured_data_batch | llm_prompt = {llm_prompt}")
            response = self.llm_client.get_llm_response(
                message_list=llm_prompt,
                model=self.llm_name_unstructured_processing,
                seed=1234,
                token_limit=4096,
            )
            llm_output = response.choices[0].message.content
            chat_log.debug(f"metalake | process_unstructured_data_batch | llm_output = {llm_output}")
            # count tokens
            # self.update_token_count(response, chat_id)
        except Exception as e:
            chat_log.error(f"metalake | process_unstructured_data_batch | error = {e}")

        if llm_output is None:
            chat_log.debug("metalake | process_unstructured_data_batch | llm_output is None")
            return -1
        elif check_unrelevent(llm_output):
            chat_log.debug("metalake | process_unstructured_data_batch | llm_output is irrelevant")
            return -1
        else:
            category_index_list_dict = get_dict_from_json_or_python(llm_output)
            chat_log.debug(f"metalake | process_unstructured_data_batch | result = {category_index_list_dict}")
            if isinstance(category_index_list_dict, dict) and all(
                isinstance(value, list) for value in category_index_list_dict.values()
            ):
                total_length = sum(len(value) for value in category_index_list_dict.values())
                chat_log.debug(
                    f"metalake | process_unstructured_data_batch | result list total_length = {total_length}"
                )
                # Arrange the output with all input fields
                processed_output = {}
                for category_key, index_list in category_index_list_dict.items():
                    processed_output[category_key] = []
                    for index_str in index_list:
                        # Make sure index_str is numeric
                        if index_str is None or (isinstance(index_str, str) and not index_str.isdigit()):
                            chat_log.warning(
                                f"metalake | process_unstructured_data_batch | index_str {index_str} is not numeric"
                            )
                            continue
                        list_index = int(index_str) - 1
                        if list_index < len(unstructured_data):
                            processed_output[category_key].append(unstructured_data[list_index])
                        else:
                            chat_log.warning(
                                f"metalake | process_unstructured_data_batch | index {list_index} is out of range"
                            )

                return processed_output

            else:
                chat_log.warn(
                    "metalake | process_unstructured_data_batch | The result is not a dictionary or contains non-list values."
                )
                return -1

    def cognitive_list_match(self, chat_id, reference_df, target_df, column_list, match_criteria):
        """
        Description:
            Perform a cognitive match on the target DataFrame for the reference DataFrame based on given match criteria and returns the matching rows as a new DataFrame.
        Parameters:
            chat_id (str): A unique identifier for the current chat session.
            reference_df (pandas.DataFrame): The reference DataFrame.
            target_df (pandas.DataFrame): The target DataFrame.
            column_list (list): The list of columns in dataframe to consider for matching
            match_criteria (str): The match criteria to use.
        Returns:
            pandas.DataFrame: A new DataFrame containing the matching rows.
        """
        import pandas as pd

        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
        # Call semantic search on each row in the reference DataFrame
        matched_records = []
        for _, item in reference_df.iterrows():  # Iterate over each row in the DataFrame
            df = self.semantic_search(target_df, column_list, item, match_criteria, chat_id, chat_log)
            if df.empty:
                continue
            # If there are more than one matches, take the first one
            df = df.head(1)
            # Merge the columns from reference_df to the matched row
            # Convert Series to DataFrame with proper column names to avoid integer column names
            item_df = pd.DataFrame([item])
            item_df.index = df.index  # Align indices for proper joining

            # Handle duplicate columns - give priority to target_df (df) values
            # Only add columns from reference_df that don't exist in target_df
            duplicate_columns = set(df.columns) & set(item_df.columns)
            if duplicate_columns:
                # Remove duplicate columns from item_df to avoid conflicts
                item_df = item_df.drop(columns=list(duplicate_columns))

            # Concatenate the DataFrames
            df = pd.concat([df, item_df], axis=1)
            matched_records.append(df)

        if not matched_records:
            return pd.DataFrame()
        # Merge the matched records into a single DataFrame
        merged_df = pd.concat(matched_records, ignore_index=True)
        return merged_df

    def semantic_search(self, df, columns_list: list, match_object: dict, match_criteria: str, chat_id: str, chat_log):
        """
        Description:
            Perform a semantic match on the specified column of the DataFrame `df` for the given object based on given match criteria and returns the matching rows as a new DataFrame.
        Parameters:
            df (pandas.DataFrame): The DataFrame to search in.
            columns_list (list): The list of columns in dataframe to consider for matching
            match_object (dict): The object to match against.
            match_criteria (str): The match criteria to use.
            chat_id (str): A unique identifier for the current chat session.
        Returns:
            pandas.DataFrame: A new DataFrame containing the matching rows.
        """
        chat_log.debug(f"metalake | semantic_search | searching for {match_object} in {columns_list}")
        # Step1: Create list of markdown tables with each row containing index and values from columns_list up to the SEMANTIC_SEARCH_TEXT_BATCH_SIZE
        # Example: [ "index | col1_header | col2_header |\n | 1 | col1_text | col2_text |\n | 2 | col1_text | col2_text |\n| 3 | ...", "index | col1_header | col2_header |\n| 1 | col1_text | col2_text |\n| 2 | col1_text | col2_text |\n| 3 | ...", ... ]
        markdown_chunks = []
        for i in range(0, len(df), SEMANTIC_SEARCH_TEXT_BATCH_SIZE):
            end = min(i + SEMANTIC_SEARCH_TEXT_BATCH_SIZE, len(df))
            chunk_df = df.iloc[i:end][columns_list]
            chunk_df.index = range(i + 1, end + 1)
            markdown_chunks.append(chunk_df.to_markdown())

        # Step2: For each chunk, call the LLM to get the list of indices that match the search query
        matched_indices = []
        for table_index, chunk in enumerate(markdown_chunks):
            indices = self._semantic_search_batch(chunk, match_object, match_criteria, chat_log)
            # Since the returning indices are 1-based within each chunk, we need to convert to 0-based indices for the original DataFrame
            start_index = table_index * SEMANTIC_SEARCH_TEXT_BATCH_SIZE
            # Convert 1-based indices to 0-based indices for the original DataFrame
            indices = [(i - 1) + start_index for i in indices]
            matched_indices.extend(indices)
        # Step3: Return the rows that match the indices
        # Filter out any indices that are out of bounds
        valid_indices = [idx for idx in matched_indices if 0 <= idx < len(df)]
        if not valid_indices:
            return df.iloc[[]]  # Return empty DataFrame if no valid indices
        return df.iloc[valid_indices]

    def _semantic_search_batch(self, markdown_table: str, match_object: dict, match_criteria: str, chat_log):
        """
        Description:
            Perform a semantic search on the specified column of the DataFrame `df` for the given object based on given match criteria and returns the matching rows as a new DataFrame.
            This function is called by semantic_search() for each batch of text data.
        Parameters:
            markdown_table (str): The markdown table to search in.
            match_object (dict): The object to match against.
            match_criteria (str): The match criteria to use.
            chat_log (Logger): The logger to use for logging.
        Returns:
            list: A list of indices that match the search query.
        """

        llm_prompt = [
            {"role": "system", "content": self.semantic_search_instructions},
            {"role": "user", "content": "search_text: " + markdown_table},
            {"role": "user", "content": "search_query: " + str(match_object)},
            {"role": "user", "content": "match_criteria: " + match_criteria},
        ]
        try:
            chat_log.debug(f"metalake | _semantic_search_batch | llm_prompt = {llm_prompt}")
            response = self.llm_client.get_llm_response(
                self,
                None,
                chat_log,
                message_list=llm_prompt,
                model=self.llm_name_semantic_search,
                response_format=cognitive_match_schema,
            )
            llm_output = response.choices[0].message.content
            chat_log.debug(f"metalake | _semantic_search_batch | llm_output = {llm_output}")
            output_dict = get_dict_from_json_or_python(llm_output)
            if output_dict == -1 or "text_indexes" not in output_dict:
                chat_log.warning(f"metalake | _semantic_search_batch | llm_output is invalid: {llm_output}")
                return []
            return output_dict["text_indexes"]
        except Exception as e:
            chat_log.error(f"metalake | _semantic_search_batch | error = {e}")
            return []

    def update_unstructured_data_labels_in_global_memory(
        self, chat_id: str, labelled_data_output: dict, chat_log: Logger
    ):
        if str(chat_id) not in self.global_memory.existing_db_unstructured_labels_for_chat:
            self.global_memory.existing_db_unstructured_labels_for_chat[str(chat_id)] = []
        for label in labelled_data_output:
            if label not in self.global_memory.existing_db_unstructured_labels_for_chat[str(chat_id)]:
                self.global_memory.existing_db_unstructured_labels_for_chat[str(chat_id)].append(label)
                chat_log.debug(f"Added label {label} to global memory for chat {chat_id}")

    def conclude_extracted_data(self, conclusion_task_instruction, user_question, chat_id):
        chat_log = get_debug_logger(f"chat_{chat_id}", f"./logs/chat_{chat_id}.log")
        instruction_file = "unstructured_data_conclude.txt"

        if chat_id not in self.global_memory.semi_structured_db_unstructured_data:
            chat_log.debug("metalake | conclude_extracted_data | chat_id not in global memory")
            return -1

        # if not intermediate_results:
        #     print("No comments found for delayed jobs in Q3 2023.")
        # else:
        #     # Merge intermediate results
        #     merged_results = {}
        #     for result in intermediate_results:
        #         for key, value in result.items():
        #             if key in merged_results:
        #                 merged_results[key].extend(value)
        #             else:
        #                 merged_results[key] = value

        intermediate_results = self.global_memory.semi_structured_db_unstructured_data[str(chat_id)][
            "intermediate_result"
        ]
        # Merge intermediate results
        merged_results = {}
        for result in intermediate_results:
            for key, value in result.items():
                if key in merged_results:
                    merged_results[key].extend(value)
                else:
                    merged_results[key] = value
        # assign merged results to later use inside conclusion python code
        self.global_memory.semi_structured_db_unstructured_data[str(chat_id)]["merged_result"] = merged_results

        extracted_data = self.global_memory.semi_structured_db_unstructured_data[str(chat_id)]["merged_result"]

        chat_log.debug(f"metalake | conclude_extracted_data | extracted data = ```json{json.dumps(extracted_data)}```")

        instructions = ""
        with open("./instructions/" + instruction_file, "r", encoding="utf-8") as file:
            instructions = file.read()

        truncated_input_data = truncate_data(extracted_data)

        instructions = instructions.replace("<<TASK_INSTRUCTION>>", str(conclusion_task_instruction))
        instructions = instructions.replace("<<USER_QUESTION>>", str(user_question))
        instructions = instructions.replace("<<TRUNCATED_INPUT_DATA>>", json.dumps(truncated_input_data))
        instructions = instructions.replace("<<CHAT_ID>>", chat_id)

        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append(
            {
                "role": "user",
                "content": "Focus to the given truncated input data. Then write a python code to get all input data and process it to give final answer",
            }
        )

        try:
            chat_log.debug(f"metalake | conclude_extracted_data | llm_prompt = {llm_prompt}")
            response = self.llm_client.get_llm_response(
                message_list=llm_prompt,
                model=self.llm_name_unstructured_processing,
                seed=1234,
                token_limit=4096,
            )
            llm_output = response.choices[0].message.content

            chat_log.debug(f"SUB LLM output of conclude_extracted_data: \n{llm_output}")
            # count tokens
            self.update_token_count(response, chat_id)
        except Exception as e:
            chat_log.warn("conclude_extracted_data: ", e)

        if llm_output is None:
            chat_log.debug(f"metalake | conclude_extracted_data | llm_output is None")
            return -1
        else:
            actions = get_python_actions(llm_output)[0]

            if actions:
                exec_output = self.execution_env.python_exec(action=actions, chat_id=chat_id)
                is_success = exec_output["success"]
                result = exec_output["output"]

                chat_log.debug(f"metalake | conclude_extracted_data | exec_output = {exec_output}")

                if is_success:
                    return result
                else:
                    return -1
            else:
                return llm_output

    def test_query_unstructured_data_by_label(
        self, unstructured_data_list, label_list, data_source_name, table_name, column_name
    ):
        label_master = self.unstructured_cache_handler.query_labelling_master(
            data_source_name, table_name, column_name, unstructured_data_list
        )
        field_id = label_master["field_id"]
        cache_result = self.unstructured_cache_handler.cache_lookup(
            unstructured_data_list, field_id, column_name, label_list
        )
        labelled_data_output = cache_result["processed_data_from_cache"]
        print(labelled_data_output)
