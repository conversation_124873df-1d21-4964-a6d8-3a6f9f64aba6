"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class AccountingDataLoaderAgent
* @description Tool responsible for loading requested data from accounting system.
* <AUTHOR>
"""

from utils.logger import get_debug_logger
from utils.misc_utils import list_to_markdown
from models.conversation import Conversation
from services.tools.base_tool import BaseTool, ToolOutput
from utils.metalake import quickbooks_query_object

"""
This is a non-intelligent tool that simply loads master data from the accounting system such as accounts, tax codes, vendors, etc.
Input: user question + data file names list
"""


class AccountingMasterDataLoaderAgent(BaseTool):
    def __init__(self):
        super().__init__()

    """
    additional_data contains the following:
    1. data_type_list: Basic types of data to load. Eg: expense_accounts, income_accounts, bank_accounts, credit_card_accounts, tax_codes, vendors, payment_methods, etc.
    """

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
        event_loop=None,
    ):
        chat_log = get_debug_logger(
            f"chat_{conversation.chat_id}", f"./logs/{base_log_path}chat_{conversation.chat_id}.log"
        )
        # Validate additional data
        if not additional_data or "data_type_list" not in additional_data:
            return ToolOutput(
                display_output="No data type provided. Please provide a valid data type to load the data.",
                result_text_list=[
                    ("No data type provided. Please provide a valid data type to load the data.", False)
                ],
            )
        data_type_list = additional_data["data_type_list"]
        if not data_type_list:
            return ToolOutput(
                display_output="No data type provided. Please provide a valid data type to load the data.",
                result_text_list=[
                    ("No data type provided. Please provide a valid data type to load the data.", False)
                ],
            )

        tool_result_to_summarize = ""
        tool_result_to_not_summarize = ""
        # Load each type of data from accounting system using metalake quickbooks_query_object function

        for data_type in data_type_list:
            query = ""
            object_type = ""
            required_fields = [
                "Id",
                "Name",
                "AccountType",
                "FullyQualifiedName",
                "Classification",
                "AccountSubType",
                "CurrencyRef",
                "Description",
            ]
            if data_type == "expense_accounts":
                query = "WHERE AccountType = 'Expense' AND Active = true"
                object_type = "Account"
            elif data_type == "income_accounts":
                query = "WHERE AccountType = 'Income' AND Active = true"
                object_type = "Account"
            elif data_type == "bank_accounts":
                query = "WHERE AccountType = 'Bank' AND Active = true"
                object_type = "Account"
            elif data_type == "credit_card_accounts":
                query = "WHERE AccountType = 'Credit Card' AND Active = true"
                object_type = "Account"
            elif data_type == "payment_methods":
                query = "WHERE Active = true"
                object_type = "PaymentMethod"
                required_fields = ["Id", "Name", "Type"]
            elif data_type == "tax_codes":
                query = "WHERE Active = true"
                object_type = "TaxCode"
                required_fields = ["Id", "Name", "Description"]
            else:
                tool_result_to_summarize += f"Data type: {data_type} is not supported.\n\n"
                tool_result_to_not_summarize += f"Data type: {data_type} is not supported.\n\n"
                continue
            try:
                res = quickbooks_query_object(object_type, query)
                if not res.get("success", False) or not res.get("data"):
                    chat_log.warning(f"Failed to load data for {data_type}. Error: {res['message']}")
                    tool_result_to_not_summarize += f"Failed to load data for {data_type}. Error: {res['message']}\n\n"
                    tool_result_to_summarize += f"Failed to load data for {data_type}.\n\n"
                    continue

                object_type_records = res["data"].get("QueryResponse", {})
                if not object_type_records or object_type not in object_type_records:
                    chat_log.warning(f"Failed to load data for {data_type}. No data returned.")
                    tool_result_to_not_summarize += f"Failed to load data for {data_type}. No data returned.\n\n"
                    tool_result_to_summarize += f"Failed to load data for {data_type}.\n\n"
                    continue
                # From the received object array, dump only the required fields to key-value object list
                data_list = []
                for record in object_type_records[object_type]:
                    for field in required_fields:
                        if field == "CurrencyRef":
                            record["CurrencyCode"] = record.get(field, {}).get("value", "")
                        else:
                            record[field] = record.get(field, "")
                    data_list.append(record)
                # Convert the data list to Markdown table
                markdown_data_list = list_to_markdown(data_list)
                tool_result_to_summarize += f"Data type: {data_type}\n\n" + markdown_data_list + "\n\n"
                tool_result_to_not_summarize += (
                    f"Data type: {data_type}\n\nRecords count = " + str(len(data_list)) + "\n\n"
                )
            except Exception as e:
                chat_log.error(f"Error loading data. Error: {str(e)}")
                tool_result_to_not_summarize += f"Error loading {data_type} data. Error: {str(e)}\n\n"
                tool_result_to_summarize += f"Error loading {data_type} data.\n\n"

        return ToolOutput(
            display_output=tool_result_to_summarize,
            result_text_list=[tuple([tool_result_to_summarize, True]), tuple([tool_result_to_not_summarize, False])],
        )
