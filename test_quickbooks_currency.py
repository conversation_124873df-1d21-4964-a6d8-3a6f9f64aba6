# Use layernext-python-sdk to test quickbooks_get_auth_uri method
# from layernext import LayerNextClient
from utils.metalake import quickbooks_get_currency_info
from utils.misc_utils import convert_currency
from utils.logger import get_debug_logger

logger = get_debug_logger("test", "./logs/test.log")
currency_code = "CAD"  # quickbooks_get_company_currency()
print(currency_code)

# Test currency conversion
amount = 100
from_currency = "USD"
to_currency = currency_code
converted_amount = convert_currency(amount, from_currency, to_currency, logger)
print(converted_amount)
