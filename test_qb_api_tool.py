import os
import shutil
from utils.constant import LLMAgentType
from models.conversation import Conversation
from models.session import Session
from services.conversation_service import ConversationService
from services.tools.data_processor.accounting_api_agent import AccountingApiAgent


# conversation = ConversationService().load_conversation("68c817bcd8c43154d3a21c6e")
# conversation = ConversationService().load_conversation(":")  # for updating
conversation = ConversationService().load_conversation("68cb68838a39825c0d84b8e9")  # for adding
# conversation = ConversationService().load_conversation("68ceaf155ab71dd5d7ccde5e")  # for matching
api_tool = AccountingApiAgent()

# # Add expenses
# result = api_tool.execute_task(
#     conversation, "statement", ["purchases.csv", "purchase_lines.csv"], "", "./", {"api_task": "add_expenses"}
# )

# # Update purchases
# result = api_tool.execute_task(
#     conversation,
#     "statement",
#     ["purchase_updates.csv"],
#     "",
#     "./",
#     {"api_task": "update_expenses"},
# )

# Add bills
result = api_tool.execute_task(
    conversation, "invoice", ["bills.csv", "bill_lines.csv"], "", "./", {"api_task": "add_bills"}
)

# # Query transactions
# result = api_tool.execute_task(
#     conversation,
#     "",
#     [],
#     "",
#     "./",
#     {
#         "api_task": "query_transactions",
#         "data_type_list": ["Purchase"],
#         "from_date": "2025-09-01",
#         "to_date": "2025-09-30",
#     },
# )

# # Match statement
# result = api_tool.execute_task(
#     conversation,
#     "statement",
#     ["float_statement_transactions.csv"],
#     "",
#     "./",
#     {"api_task": "match_statement", "data_type_list": ["Purchase"]},
# )

# # result = api_tool.review_created_entries("Purchase", "2025-09-09T13:07:50.470252-05:00", ["41", "42"], None)

print(result)
